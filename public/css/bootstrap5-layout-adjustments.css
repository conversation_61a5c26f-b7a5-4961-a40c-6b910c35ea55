/**
 * Bootstrap 5 Layout Adjustments
 *
 * This file contains specific adjustments to make the Bootstrap 5 layout
 * visually consistent with the Bootstrap 4 layout, particularly for
 * navigation elements, logo sizing, and other layout-specific tweaks.
 */

/* Navbar height and logo adjustments */
.sb-topnav.navbar {
    height: 56px; /* Match BS4 navbar height */
    padding-top: 0;
    padding-bottom: 0;
    position: relative; /* Ensure proper positioning context */
}

/* Logo sizing and positioning */
.class3logo {
    position: absolute;
    width: 14.0625rem; /* 225px */
    height: 3.5rem; /* 56px - match navbar height */
    left: 0;
    top: 0;
}

.class4logo {
    position: absolute;
    height: 3.5rem; /* 56px - match navbar height */
    left: 0;
    top: 0;
}

/* Navbar brand padding adjustment */
.navbar-brand {
    padding-top: 0;
    padding-bottom: 0;
    height: 56px;
    display: flex;
    align-items: center;
}

/* Navbar toggler positioning */
.navbar-toggler {
    padding: 0.5rem;
    margin-right: 1rem;
}

/* Navbar nav item spacing */
.navbar-nav .nav-item {
    display: flex;
    align-items: center;
}

/* Adjust navbar items to start after the logo */
.navbar-collapse {
    margin-left: 180px; /* Same width as the logo */
    z-index: 1030; /* Ensure navbar collapse appears above content */
}

/* Ensure navbar items are properly aligned */
.navbar-nav {
    padding-left: 1rem;
}

/* Dropdown menu adjustments */
.dropdown-menu {
    margin-top: 0.5rem;
    padding: 0.5rem 0;
    top: 100%; /* Position below the parent item */
    transform: none !important; /* Prevent Bootstrap 5's transform positioning */
    background-color: var(--class-dark-blue);
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    color: #f8f9fa;
}

/** Create a dropdown item non-clickable. To be used -instead of- .dropdown-item */
.dropdown-item-nc {
    cursor: default;
}
.dropdown-item-nc:hover {
    background-color: transparent;
    color: #f8f9fa;
}


/* Fix for dropdown positioning */
.dropdown {
    position: relative;
}

/* Add dropdown indicator */
.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

/* Ensure dropdowns don't render off-screen */
.dropdown-menu {
    max-height: calc(100vh - 100px); /* Prevent extremely tall dropdowns */
    overflow-y: auto; /* Allow scrolling for tall dropdowns */
}

/* Fix for nested dropdowns */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -0.5rem;
    margin-left: 0.125rem;
}

/* Sidebar adjustments */
#layoutSidenav {
    display: flex;
}

#layoutSidenav_content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
    flex-grow: 1;
    min-height: calc(100vh - 56px);
    margin-left: 0;
}

/* Footer adjustments */
footer.py-4 {
    margin-top: auto;
}

/* Card header adjustments */
.card-header {
    padding: 0.75rem 1.25rem;
    height: auto;
    display: flex;
    align-items: center;
}

/* Fix for icon spacing in buttons and headers */
.card-header i,
.btn i {
    margin-right: 0.5rem;
}

/* Adjust spacing for icons in nav links */
.nav-link i {
    margin-right: 0.5rem;
    width: 1.25em;
    text-align: center;
}

/* Adjust form control heights to match BS4 */
.form-control,
.form-select {
    min-height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
}

/* Fix Bootstrap 5 form-select dropdown arrow overlapping text */
.form-select {
    padding-right: 2.25rem; /* Ensure enough space for the dropdown arrow */
    background-position: right 0.75rem center; /* Position arrow properly */
    background-size: 16px 12px; /* Ensure arrow is properly sized */
}

/* Ensure select options don't have z-index conflicts */
.form-select option {
    background-color: #fff;
    color: #212529;
    padding: 0.375rem 0.75rem;
}

/* Fix form-select positioning within card headers and flex containers */
.card-header .form-select {
    position: relative;
    z-index: 1;
    min-width: 200px; /* Ensure adequate width for dropdown */
}

/* Ensure proper stacking context for form elements in flex containers */
.d-flex .form-select {
    position: relative;
    z-index: 1;
}

/* Specific fix for justify-content-between layouts with form-select */
.d-flex.justify-content-between .form-select {
    flex-shrink: 0; /* Prevent the select from shrinking */
    max-width: none; /* Allow full width if needed */
}

/* Adjust button padding to match BS4 */
.btn {
    padding: 0.375rem 0.75rem;
}

/* Adjust table cell padding to match BS4 */
.table td,
.table th {
    padding: 0.75rem;
}

/* Adjust modal header and footer to match BS4 */
.modal-header,
.modal-footer {
    padding: 1rem;
}

.modal-body {
    padding: 1rem;
}

/* Adjust alert padding to match BS4 */
.alert {
    padding: 0.75rem 1.25rem;
}

/* Adjust badge padding to match BS4 */
.badge {
    padding: 0.25em 0.4em;
}

/* Adjust dropdown menu item padding to match BS4 */
.dropdown-item {
    padding: 0.25rem 1.5rem;
}

/* Adjust list group item padding to match BS4 */
.list-group-item {
    padding: 0.75rem 1.25rem;
}

/* Adjust input group text padding to match BS4 */
.input-group-text {
    padding: 0.375rem 0.75rem;
}

/* Adjust breadcrumb padding to match BS4 */
.breadcrumb {
    padding: 0.75rem 1rem;
}

/* Adjust pagination padding to match BS4 */
.page-link {
    padding: 0.5rem 0.75rem;
}

/* Adjust tooltip and popover to match BS4 */
.tooltip-inner {
    padding: 0.25rem 0.5rem;
}

.popover {
    padding: 0;
}

.popover-header {
    padding: 0.5rem 0.75rem;
}

.popover-body {
    padding: 0.5rem 0.75rem;
}

/* Adjust progress bar height to match BS4 */
.progress {
    height: 1rem;
}

/* Adjust spinner size to match BS4 */
.spinner-border,
.spinner-grow {
    width: 2rem;
    height: 2rem;
}

/* Adjust toast padding to match BS4 */
.toast {
    padding: 0.25rem;
}

.toast-header {
    padding: 0.25rem 0.75rem;
}

.toast-body {
    padding: 0.75rem;
}

/* Adjust accordion padding to match BS4 */
.accordion-button {
    padding: 0.75rem 1.25rem;
}

.accordion-body {
    padding: 1.25rem;
}

/* Adjust offcanvas padding to match BS4 */
.offcanvas-header {
    padding: 1rem;
}

.offcanvas-body {
    padding: 1rem;
}

/* Adjust carousel caption padding to match BS4 */
.carousel-caption {
    padding: 1.25rem;
}

/* Adjust card padding to match BS4 */
.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
}

/* Adjust jumbotron replacement padding to match BS4 */
.bg-light.p-5 {
    padding: 4rem !important;
}

/* Adjust navbar dropdown menu position */
.navbar .dropdown-menu {
    position: absolute;
}

/* Fix for navbar dropdown on mobile */
@media (max-width: 992px) {
    .navbar-collapse {
        max-height: calc(100vh - 56px);
        overflow-y: auto;
        margin-left: 0; /* Reset margin on mobile */
        padding-top: 1rem; /* Add some space at the top */
        position: absolute; /* Position absolutely to overlay content */
        top: 56px; /* Position below the navbar */
        left: 0;
        right: 0;
        background-color: var(--class-dark-blue); /* Match navbar color */
        z-index: 1030; /* Ensure it appears above content */
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); /* Add shadow for depth */
    }

    .navbar .dropdown-menu {
        position: static;
        float: none;
        margin-top: 0;
        margin-bottom: 0.5rem;
        background-color: rgba(0, 0, 0, 0.15); /* Slightly darker background for nested menus */
        border: none;
        box-shadow: none;
    }

    /* Improve dropdown item visibility on mobile */
    .navbar .dropdown-item {
        color: rgba(255, 255, 255, 0.85);
        padding: 0.5rem 1.5rem;
    }

    .navbar .dropdown-item:hover,
    .navbar .dropdown-item:focus {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    /* Adjust navbar items on mobile */
    .navbar-nav {
        padding-left: 0;
    }

    /* Ensure logo doesn't overlap with toggler on mobile */
    .navbar-brand {
        max-width: calc(100% - 80px);
    }

    /* Adjust spacing for collapsed navbar */
    .navbar-nav .nav-item {
        margin-left: 0;
        padding-left: 1rem;
    }
}
