import { nextTick } from "vue";
import axios from "axios";
import { oneStudentGroup } from "../mocks/OneStudentGroup";
import { vi } from 'vitest';

// Mock useToast before importing useEditStudentGroup
vi.mock('../../../resources/js/class3/composables/useToast.js', () => {
    return {
        default: () => ({
            successToast: vi.fn(),
            failToast: vi.fn()
        })
    };
});

import useEditStudentGroup from "../../../resources/js/class3/composables/useEditStudentGroup";

const flushPromises = require('flush-promises');
const {
    addCourseToStudentGroup,
    addStudentToStudentGroup,
    appointments,
    course,
    getStudentGroup,
    removeCourseFromStudentGroup,
    removeStudentFromStudentGroup,
    saveStudentGroup,
    students,
    studentGroup
} = useEditStudentGroup();

// mock axios
vi.mock('axios');
afterEach(() => {
    vi.resetAllMocks();
});

axios.put = vi.fn();
axios.get = vi.fn();
axios.delete = vi.fn();
describe('useEditStudentGroup', () => {
    describe('saveStudentGroup', () => {
        it('should call failNoty when input is {}', async () => {
            await saveStudentGroup({});
            await flushPromises();
        });
        it('should call axios.put when input is {courseId: 1}', async () => {
            // first setup a student group
            studentGroup.value = {id: 1};
            // now wait for 'editMode' (computed) to be true
            await nextTick();
            // then call saveStudentGroup
            await saveStudentGroup({courseId: 1, lastname: 'test'});
            // and check if axios.put was called with the right parameters
            expect(axios.put).toHaveBeenCalledWith('/api/studentgroups/1', {lastname: 'test', courseId: 1});
        });
    });
    describe('getStudentGroup', () => {
        it ('should fail silently if there is no studentgroup id', async () => {
            studentGroup.value = {};
            await getStudentGroup();
            await flushPromises();
            expect(studentGroup.value).toEqual({});
        });
        it('should get a studentgroup and fill properties if studentgroup id > 0', async () => {
            axios.get.mockResolvedValue({data:{data: oneStudentGroup}});
            studentGroup.value = {id: 1};
            await getStudentGroup();
            await flushPromises();
            expect(studentGroup.value.name).toBe(oneStudentGroup.name);
            expect(course.value.name).toBe(oneStudentGroup.course.name);
            expect(appointments.value).toBe(`${oneStudentGroup.future_appointments} / ${oneStudentGroup.appointments}`);
            expect(students.value.length).toBe(29);
        });
    });
    describe('addCourseToStudentGroup', () => {
        it('should call axios.put with the right parameters', async () => {
            studentGroup.value = {id: 1};
            await addCourseToStudentGroup(1);
            await flushPromises();
            expect(axios.put).toHaveBeenCalledWith('/api/studentgroups/1', {courseId: 1});
        });
    });
    describe('addStudentToStudentGroup', () => {
        it('should call axios.put with the right parameters', async () => {
            studentGroup.value = {id: 1};
            await addStudentToStudentGroup(1);
            await flushPromises();
            expect(axios.put).toHaveBeenCalledWith('/api/studentgroups/1', {studentId: 1});
        });
    });
    describe('removeCourseFromStudengroup', () => {
        it('should call axios.delete with the right parameters', async () => {
            studentGroup.value = {id: 1};
            await removeCourseFromStudentGroup(1);
            await flushPromises();
            expect(axios.delete).toHaveBeenCalledWith('/api/removecoursefromstudentgroup/1/1');
        });
    });
    describe('removeStudentFromStudentGroup', () => {
        it('should call axios.delete with the right parameters', async () => {
            studentGroup.value = {id: 1};
            await removeStudentFromStudentGroup(1);
            await flushPromises();
            expect(axios.delete).toHaveBeenCalledWith('/api/removestudentfromstudentgroup/1/1');
        });
    });
});
