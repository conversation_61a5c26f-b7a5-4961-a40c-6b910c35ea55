# Class Project Guidelines

## general rules for Vue
- Use Vue Single File Components (SFCs) for all new UI components
- Use Composition API instead of Options API for all Vue components
- Prefer async/await over promise chains
- Follow the existing project structure where components are organized by feature
- When refactoring server-side code to client-side, ensure API endpoints return JSON
- Don't use Pinia or Vuex, we keep state in composables.
- Always use <script setup> instead of the setup() method
- Always order an SFC as 1. template, 2. script, 3. css
- Name composables with the `use` prefix (e.g., `useStudents`, `useTimetableReport`)
- Place composables in the `resources/js/class3/composables` directory
- Composables should handle API calls, state management, and business logic, see examples below
- Components should get their data from composables, not via props from parent components
- For computed properties, don't use the 'get' prefix (use 'studentName' instead of 'getStudentName')
- Only use the 'get' prefix for methods that make API calls or fetch data
- The return fields in composables are always in alphabetical order
- Variables in composables that will be imported in components are placed outside the function useName() scope so they are in global scope.
- never use a <form> tag in a Vue component. Always use a handler to create api calls. This is because we will never sumit the form in the traditional HTML was, so we will not suggest to fellow developers there is a submittable form. instead, simply use @click on button and similar.
- Never add translations to the translations files (in the lang directory). We use an external tool to manage translations. Instead, list the necessary translations in the Vue component to be added manually by me.
- Composables are only allowed to expose their own variables  (computed, ref or constant), never expose data from imported composables that the composables needs. The calling code should import the composable.

Here's an example of a composable:
``` js
import { ref } from 'vue';
import axios from 'axios';
import useToast from './useToast';
const students = ref([]);
const studentToEdit = ref({});

export default function useStudents() {
    import { failToast } from useToast;

    const getStudents = async () => {
        try {
            const response = await axios.get('/api/students');
            students.value = response.data;
        } catch (error) {
            throw error;
        }
    };

    const saveStudent = async () => {
        try {
            await axios.post('/api/students', studentToEdit.value   );
            studentToEdit.value = {};
        } catch (error) {
            throw error;
        }
    };

    const isSaveButtonDisabled = computed(() => {
        return !studentToEdit.value.name || !studentToEdit.value.email;
    });

    return {
        getStudents,
        saveStudent,
        students,
        studentToEdit,
        isSaveButtonDisabled
    };
}
```
Here is an example of a component that uses the composable:
```vue  
<template>
    <div>
        <h1>Students</h1>
        <ul>
            <li v-for="student in students" :key="student.id">
                {{ student.name }}
            </li>
        </ul>
        <!-- don't use a form tag here! we will never submit it in the traditional HTML way -->
        <div>
            <input type="text" v-model="studentToEdit.name" />
            <button @click="saveStudent" :disabled="isSaveButtonDisabled">Save</button>
        </div>
   </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import useStudents from '../composables/useStudents.js';
import useToast from '../composables/useToast.js';
const { getStudents, saveStudent, students, studentToEdit, isSaveButtonDisabled } = useStudents();
const { successToast, failToast } = useToast();

const saveStudent = async () => {
    try {
        await saveStudent(); // this is the function from the composable, no need to send it data
        successToast('Student saved successfully');
    } catch (error) {
        failToast(error);
    }
};
onMounted(() => {
    try {
        getStudents();
    } catch (error) {
        failToast(error);
    }
});
</script>
```
An important takeaway here is the composable does not show any interface interaction. They only contain the logic and data.
The component that uses the composable shows the interface interaction by using successToast and failToast.
Generally we try to return success or fail from api calls in composables, and on success fill the data (variable) that is also part of the composable. The component uses the function and the data from the composable, rather than having the local variable itself. As a result there is hardly any logic in the component.

## embedding in the laravel backend
- We use a root component (aka page) for each page in the app. The root component is responsible for fetching data by calling the get function in a composable.
- The child components will use the data from the composable.
- We minimize the use of properties and methods in the root component. most communication is done using the composable
- in laravel we use the router and controller to start a blade template that contains the root component.
- There is usually no data passed to the blade template (there are exceptions). The vue component will fetch the data from the API which is just another route to the controller.
- The methods in the controller for api calls are prefixed with api, like apiIndex, apiStore, apiUpdate, apiDestroy, apiShow, apiGet

