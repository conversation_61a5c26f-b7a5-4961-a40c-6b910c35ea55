import { ref } from "vue";
import useLang from "./useLang.js";
import useToast from "./useToast.js";
import useApi from "./useApi.js";
import useBaseData from "./useBaseData.js";

const { ucFirst, translate} = useLang();
const { failToast, successToast } = useToast();
const { initBaseData } = useBaseData();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

const locationToEdit = ref({});
const locationIdToDelete = ref(null);

export default function useLocation() {
    const saveLocationData = async () => {
        let url = '';
        let data = { name: locationToEdit.value.name };
        try {
            if(locationToEdit.value.id) {
                url = `/locations/${locationToEdit.value.id}`;
                await apiPut(url, data);
            } else {
                url = '/locations';
                await apiPost(url, data);
            }
            successToast(ucFirst(translate('generic.datasaved')));
            updateLocations();
        } catch (error) {
            failToast(ucFirst(translate('generic.error')) + ' ' + error.message);
        }
    };

    const createNewLocation = () => {
        locationToEdit.value = {};
        showEdit.value = true;
    }

    const deleteLocation = async () => {
        try {
            await apiDel(`/locations/${locationIdToDelete.value}`);
            updateLocations();
            successToast(ucFirst(translate('generic.locationdeleted')));
        } catch (error) {
            failToast(ucFirst(translate('generic.error')) + ' ' + error.message);
        }
    }

    const showEdit = ref(false);

    const editLocation = (location) => {
        locationToEdit.value = location;
        showEdit.value = true;
    }

    const updateLocations = async () => {
        locationToEdit.value = {};
        await initBaseData({locations: true}, true);
        showEdit.value = false;
    }

    return {
        createNewLocation,
        deleteLocation,
        editLocation,
        locationIdToDelete,
        locationToEdit,
        saveLocationData,
        showEdit,
        updateLocations
    }
}
