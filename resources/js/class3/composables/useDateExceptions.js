import { computed, ref } from "vue";
import useApi from "./useApi.js";
import useBaseData from '../composables/useBaseData.js';
import useLang from "./useLang.js";
import useToast from "./useToast.js";

const { apiDel, apiGet } = useApi();
const { allDateExceptions } = useBaseData();
const { ucFirst, translate } = useLang();
const { failToast } = useToast();

const lessonConflicts = ref([]);
const dateExceptionToDelete = ref(0);
const dateExceptionIdToEdit = ref(0);
const tutorId = ref(0);
const chosenYearId = ref(0);


export default function useDateExceptions() {

    /**
     * filter school year into dateExceptionsByYear by comparing with the currently chosen year id
     * filter that list on tutors, where 0 means all tutors (including entire school)
     * and -1 means only "entire school" exceptions.
     * if tutor id other than 0 or -1: show the date exceptions of the selected tutor.
     * @returns {*[]}
     */
    const filteredDateExceptions = computed(() => {
        const dateExceptionsByYear = allDateExceptions.value.filter(de => de.schoolyear_id === chosenYearId.value);
        if (tutorId.value === 0) return dateExceptionsByYear;
        if (tutorId.value === -1) return dateExceptionsByYear.filter(de => de.tutors.length === 0);
        return dateExceptionsByYear.filter(de => de.tutors.some(tutor => tutor.id === tutorId.value));
    });

    const deleteDateException = async () => {
        try {
            await apiDel(`/api/dateexceptions/${dateExceptionToDelete.value}`);
        } catch (error) {
            throw error;
        }
    };

    /**
     * get the lesson conflicts for all date exceptions from the server
     */
    const getLessonConflicts = async () => {
        lessonConflicts.value = [];
        for (const de of filteredDateExceptions.value) {
            try {
                const response = await apiGet(`/api/dateexceptions/${de.id}/lessonconflicts`);
                lessonConflicts.value.push(response.data);
            } catch (error) {
                failToast(
                    error.message,
                    ucFirst(translate('generic.error'))
                );
            }
        }
    };

    /**
     * show the number of conflicts for a date exception
     * if there are any sticky events, count them separately so we know if all conflicts have been resolved
     * @param dateExceptionId
     * @returns {{result: string, code: string}|{result: string, code: (string)}}
     */
    const showConflictsForDateException = (dateExceptionId) => {
        // CLASS colors
        const colorTable = {
            green: '#4C9B5E',
            red: '#C75454'
        };
        const conflicts = lessonConflicts.value.find(lc => {
            return parseInt(lc.dateExceptionId) === parseInt(dateExceptionId)
        });
        if (!conflicts) return {code: colorTable.green, result: "0/0"};
        // if conflicts.conflicts is an object, turn it into an array
        // turns out this only happens when we have 1 conflict
        if (!Array.isArray(conflicts.conflicts)) {
            conflicts.conflicts = [conflicts.conflicts];
        }
        const numberOfEventsThatAreSticky = conflicts.conflicts.filter(e => e.flag_sticky).length;
        return {
            code: conflicts.conflicts.length > numberOfEventsThatAreSticky ? colorTable.red : colorTable.green,
            result: `${conflicts.conflicts.length}&nbsp;/&nbsp;${numberOfEventsThatAreSticky}`
        };
    };

    return {
        chosenYearId,
        dateExceptionIdToEdit,
        dateExceptionToDelete,
        deleteDateException,
        filteredDateExceptions,
        getLessonConflicts,
        lessonConflicts,
        showConflictsForDateException,
        tutorId,
    }
}
