import { nextTick, ref, watch } from 'vue';
import useToast from './useToast.js';
import useLang from './useLang.js';
import useApi from './useApi.js';

const idToEdit = ref(0);
const taskToDelete = ref(0);
const task = ref({});
const busy = ref(false);
const dirty = ref(false);
const tasks = ref([]);
const semaphore = ref(false); // prevent double initialisation

export default function useEditTask () {
    const { failToast, successToast } = useToast();
    const { ucFirst, translate } = useLang();
    const { apiGet, apiPut, apiDel } = useApi();

    const getAllTasks = async (force = true) => {
        if ((force || tasks.value.length === 0) && !semaphore.value) {
            semaphore.value = true;
            try {
                const resp = await apiGet('/api/gettasks');
                tasks.value = resp.data.data;
            } catch (error) {
                console.error(error);
            } finally {
                semaphore.value = false;
            }
        }
    };

    watch(idToEdit, async (value, oldValue) => {
        if (value !== oldValue && value !== 0) {
            if (idToEdit.value === -1) {
                console.log('NEW TASK');
                // if and when we need this function: implement
            } else if (idToEdit.value === 0) {
                console.log('RESET');
                task.value = {};
            } else if (idToEdit.value !== task.value?.id) {
                console.log('EDIT TASK');
                try {
                    busy.value = true;
                    const response = await apiGet(`/api/gettask/${idToEdit.value}`);
                    task.value = response.data.data;
                    dirty.value = false;
                    console.log('TASK received:', task.value);
                    // now scroll to the edit section
                    nextTick(() => {
                        document.getElementById('targetForScrollIntoView')
                            .scrollIntoView({ behavior: 'smooth' });
                    });
                } catch (err) {
                    console.log(err);
                } finally {
                    console.log('FINALLY');
                    busy.value = false;
                }
            } else {
                console.log('NO ACTION SELECTED');
            }
        }
    });

    /**
     * save current Task data
     * @returns {Promise<void>}
     */
    const saveTask = async () => {
        if (!task.value?.id || task.value.id === -1) {
            // POST / NEW
            // if and when we need this function: implement
        } else {
            // PUT / UPDATE
            busy.value = true;
            console.log('PUT', task.value.id, task.value);
            try {
                await apiPut(`/api/task/${task.value?.id}`, task.value);
                successToast(ucFirst(translate('generic.savesuccess')),
                    ucFirst(translate('generic.success')), 3000);
                task.value = {};
                idToEdit.value = 0;
                // on success: refresh list
                await getAllTasks(true);
                dirty.value = false;
            } catch (err) {
                failToast(ucFirst(translate('generic.savingfailed')) + ': ' + err,
                    ucFirst(translate('generic.fail')));
            } finally {
                busy.value = false;
            }
        }
    };

    const deleteTask = async () => {
        if (taskToDelete.value > 0) {
            busy.value = true;
            try {
                await apiDel(`/api/task/${taskToDelete.value}`);
                successToast(
                    ucFirst(translate('generic.taskdeleted')),
                    ucFirst(translate('generic.success')),
                    3000
                );
                // on success: refresh list
                await getAllTasks(true);
                // if we have this task in the edit section: close the edit section
                if (task.value.id != null && taskToDelete.value === task.value.id) {
                    task.value = null;
                }
                taskToDelete.value = 0;
                dirty.value = false;
            } catch (err) {
                failToast(
                    ucFirst(translate('generic.unabletodeletetutor') + ': ' + err),
                    ucFirst(translate('generic.deletefailed'))
                );
            } finally {
                busy.value = false;
            }
        }
    };

    return {
        taskToDelete,
        tasks,
        busy,
        deleteTask,
        dirty,
        getAllTasks,
        idToEdit,
        saveTask,
        task
    };
};
