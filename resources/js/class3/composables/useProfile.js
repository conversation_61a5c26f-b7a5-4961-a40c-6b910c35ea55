import { computed, ref } from "vue";
import useToast from "./useToast.js";
import useLang from "./useLang.js";
import useApi from "./useApi.js";

const { ucFirst, translate } = useLang();
const { failToast, successToast } = useToast();
const { apiGet, apiPut } = useApi();
const profile = ref(null);
const busy = ref(false);

export default function useProfile() {
    const getProfile = async () => {    
        busy.value = true;
        try {
            const response = await apiGet('/api/profile');
            profile.value = response.data;
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
        }
    };
    
    const saveProfile = async () => {
        busy.value = true;
        try {
            await apiPut('/api/profile', profile.value);
            successToast(ucFirst(translate('generic.profileupdatedsuccessfully')));
        } catch (error) {
            failToast(error.response.data.message);
        } finally {
            busy.value = false;
        }
    };

    const saveable = computed(() => {
        return profile.value?.name !== '' && profile.value?.email !== '';
    });

    return {
        busy,
        profile,
        getProfile,
        saveable,
        saveProfile,
    };
}
