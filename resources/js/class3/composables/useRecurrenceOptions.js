import { ref } from 'vue';
import useToast from "./useToast.js";
import useLang from "./useLang.js";
import useApi from "./useApi.js";

const { failToast, successToast } = useToast();
const { translate, translateChoice, ucFirst } = useLang();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

const busy = ref(false);
const searchRecurrenceOptions = ref('');
const recurrenceOptions = ref([]);
const recurrenceOptionIdToDelete = ref(0);
const recurrenceOptionToEdit = ref(null);

const timeunits = [
    { label: "hours", value: "hour" },
    { label: "minutes", value: "minutes" }
];
const per_intervals = [
    { label: "perday", value: "day" },
    { label: "perweek", value: "week" },
    { label: "pertwoweeks", value: "two weeks" },
    { label: "permonth", value: "month" },
    { label: "doesnotapply", value: "notapply" }
];

const emptyRecurrenceOption = {
    id: null,
    nr_of_times: 1,
    timeunit: "hour",
    per_interval: "day",
    ends_after_nr_of_occurrences: 0
};

export default function useRecurrenceOptions() {

    const getRecurrenceOptions = async () => {
        busy.value = true;
        try {
            const response = await apiGet('/api/recoptions');
            recurrenceOptions.value = response.data;
        } catch (error) {
            failToast(error);
        } finally {
            busy.value = false;
        }
    }

    const removeRecurrenceOption = async () => {
        try {
            busy.value = true;
            await apiDel(`/api/recoptions/${recurrenceOptionIdToDelete.value}`);
            await getRecurrenceOptions();
            successToast(ucFirst(translateChoice('generic.recurrenceoptions', 1)) + ' ' + translate('generic.deleted'));
        } catch (error) {
            failToast(error);
        } finally {
            busy.value = false;
        }
    };

    const saveRecurrenceOption = async () => {
        /* create a description for convenience
        for instance
        1 uur per dag en eindigt na 1 herhaling
        1 uur per twee weken tot uitschrijven (doorlopend)
        but if we have notapply fo per_interval, we don't have a per_interval, so that would be:
        1 uur en eindigt na 1 herhaling
        */
        let recDescription = "";

        const nrOfTimes = recurrenceOptionToEdit.value.nr_of_times === "" ? "0" : recurrenceOptionToEdit.value.nr_of_times;
        const timeUnit = timeunits.find(tu => tu.value === recurrenceOptionToEdit.value.timeunit).label;
        const perInterval = per_intervals.find(pi => pi.value === recurrenceOptionToEdit.value.per_interval).label;
        if (recurrenceOptionToEdit.value.ends_after_nr_of_occurrences == null || recurrenceOptionToEdit.value.ends_after_nr_of_occurrences === 0) {
            recDescription =
                `${ nrOfTimes } ${ translateChoice('generic.' + timeUnit, nrOfTimes) } `
                + (recurrenceOptionToEdit.value.per_interval !== "notapply" ? 
                   `${ translate('generic.per') } ${ translate('generic.' + perInterval) } ` : 
                   ``)
                + `${ translate('generic.until_unregister_indefinitely') }`;
        } else {
            recDescription =
                `${ nrOfTimes } ${ translateChoice('generic.' + timeUnit, nrOfTimes) } `
                + (recurrenceOptionToEdit.value.per_interval !== "notapply" ?
                    `${ translate('generic.per') } ${ translate('generic.' + perInterval) } ` :
                    ``)
                + `${ translate('generic.and') } `
                + `${ translate('generic.endsafter') } `
                + `${ recurrenceOptionToEdit.value.ends_after_nr_of_occurrences } `
                + `${ translateChoice('generic.repetitions', recurrenceOptionToEdit.value.ends_after_nr_of_occurrences) }`;
        }
        recurrenceOptionToEdit.value.description = recDescription;

        try {
            busy.value = true;
            if (recurrenceOptionToEdit.value.id > 0) {
                // update record
                await apiPut(`/api/recoptions/${recurrenceOptionToEdit.value.id}`, recurrenceOptionToEdit.value);
            } else {
                // new record
                await apiPost('/api/recoptions', recurrenceOptionToEdit.value);
            }
            // retrieve the new list from the backend
            await getRecurrenceOptions();
        } catch (error) {
            throw error;
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        emptyRecurrenceOption,
        getRecurrenceOptions,
        per_intervals,
        recurrenceOptions,
        recurrenceOptionIdToDelete,
        recurrenceOptionToEdit,
        removeRecurrenceOption,
        saveRecurrenceOption,
        searchRecurrenceOptions,
        timeunits,
    };
}
