import { ref } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from './useLang.js';

// Create reactive state outside the function to share state between components
const busy = ref(true);
const individualTimetables = ref([]);
const groupTimetables = ref([]);
const schoolyear = ref({});

export default function useTimetableReport() {
    const { apiGet } = useApi();
    const { failToast } = useToast();
    const { translate } = useLang();

    // Fetch event summary for a specific registration
    const fetchEventSummary = async (registrationId, schoolYearId) => {
        try {
            const response = await apiGet(`/api/eventsreginsy`, {
                params: {
                    registrationId,
                    schoolYearId,
                    onlySummary: true
                }
            });
            return response.data.summary;
        } catch (error) {
            console.error(`Error fetching event summary for registration ${registrationId}:`, error);
            return {
                blocked: 0,
                past: 0,
                future: 0,
                'total-events': 0,
                'total-occuring': 0
            };
        }
    };

    // Fetch event summaries for all timetables
    const fetchEventSummaries = async (timetables, schoolYearId) => {
        const updatedTimetables = [];

        for (const timetable of timetables) {
            const summary = await fetchEventSummary(timetable.registration_id, schoolYearId);
            updatedTimetables.push({
                ...timetable,
                event_summary: summary
            });
        }

        return updatedTimetables;
    };

    const fetchTimetablesData = async (schoolyearId = null) => {
        try {
            busy.value = true;
            const url = schoolyearId
                ? `/api/timetables/report/${schoolyearId}`
                : '/api/timetables/report';

            const response = await apiGet(url);
            schoolyear.value = response.data.schoolyear;

            // Fetch event summaries for individual timetables
            individualTimetables.value = await fetchEventSummaries(
                response.data.individual_timetables,
                schoolyear.value.id
            );

            // Fetch event summaries for group timetables
            groupTimetables.value = await fetchEventSummaries(
                response.data.group_timetables,
                schoolyear.value.id
            );
        } catch (error) {
            console.error('Error fetching timetables data:', error);
            failToast(translate('generic.errorloadingtimetables'));
        } finally {
            busy.value = false;
        }
    };

    return {
        busy,
        fetchTimetablesData,
        groupTimetables,
        individualTimetables,
        schoolyear
    };
}
