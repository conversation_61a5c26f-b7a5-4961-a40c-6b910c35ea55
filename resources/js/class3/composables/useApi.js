import axios from 'axios';
import useLang from "./useLang.js";

const { translate, ucFirst } = useLang();

// Configure the default axios instance
axios.defaults.timeout = 120000; // 2 minutes
axios.defaults.withCredentials = true;
axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest';
axios.defaults.headers['Content-Type'] = 'application/json';
axios.defaults.headers['Accept'] = 'application/json';
axios.defaults.retry = 3; // Set default retry count

// Add CSRF token to all requests
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error(ucFirst(translate('api.noCSRFToken')));
}

// Add response interceptor for error handling
axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            if (error.response.status === 401) {
                // Handle unauthorized access
                window.location.href = '/login';
            } else if (error.response.status === 419) {
                // CSRF token mismatch - just throw the error
                error.message = ucFirst(translate('api.sessionExpired'));
            } else if (error.response.status >= 500) {
                // Server error - just throw the error
                error.message = ucFirst(translate('api.serverError'));
            }
        } else if (error.request) {
            // The request was made but no response was received
            error.message = ucFirst(translate('api.noResponseFromServer'));
        } else {
            // Something happened in setting up the request
            error.message = `${ ucFirst(translate('api.errorSettingUpRequest')) }: ${ error.message }`;
        }
        return Promise.reject(error);
    }
);

export default function useApi() {
    // Helper methods for common API operations
    const apiGet = async (url, config = {}) => {
        // Only pass config if it's not empty
        return Object.keys(config).length === 0
            ? await axios.get(url)
            : await axios.get(url, config);
    };

    const apiPost = async (url, data = {}, config = {}) => {
        // Only pass config if it's not empty
        return Object.keys(config).length === 0
            ? await axios.post(url, data)
            : await axios.post(url, data, config);
    };

    const apiPut = async (url, data = {}, config = {}) => {
        // Only pass config if it's not empty
        return Object.keys(config).length === 0
            ? await axios.put(url, data)
            : await axios.put(url, data, config);
    };

    const apiDel = async (url, config = {}) => {
        // Only pass config if it's not empty
        return Object.keys(config).length === 0
            ? await axios.delete(url)
            : await axios.delete(url, config);
    };

    return {
        api: axios, // Expose the configured axios instance
        apiGet,
        apiPost,
        apiPut,
        apiDel
    };
}

// Export the axios instance directly for simpler imports
export { axios as api };
