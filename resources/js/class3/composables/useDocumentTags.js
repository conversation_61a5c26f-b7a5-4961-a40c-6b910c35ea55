import { ref, computed } from "vue";
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from "./useLang.js";

/**
 * Document Tags Composable
 * 
 * This composable manages tags for documents in the library system.
 * It provides functions for CRUD operations on tags and their associations with documents.
 * 
 * Key Functions:
 * 
 * TAG MANAGEMENT:
 * - getTags(): Fetches all available tags from the API
 * - createNewTag(tagName): Creates a new tag via API and adds it to the options list
 * 
 * DOCUMENT-TAG ASSOCIATIONS:
 * - getDocumentTags(documentId): Fetches tags for a specific document
 * - addTagToDocument(documentId, tagName): API call to add a tag to a document
 * - apiRemoveTagFromDocument(documentId, tagId): Internal API call to remove a tag from a document
 * - removeTagFromDoc(documentId, tagId, document): Public function that removes a tag AND updates local state
 * 
 * BULK OPERATIONS:
 * - initializeDocumentTags(documents): Loads tags for multiple documents at once
 * - updateDocumentTags(documentId, selectedTagIds, document): Handles multiselect changes (add/remove multiple tags)
 * 
 * DROPDOWN STATE MANAGEMENT:
 * - openDropdownId: ref(null) - Tracks which dropdown is currently open
 * - setDropdownOpen(documentId): Sets a dropdown as open and closes others
 * - setDropdownClosed(documentId): Closes a specific dropdown
 * - isDropdownOpen(documentId): Checks if a specific dropdown should be open
 *
 * STATE MANAGEMENT:
 * - tags: ref([]) - All available tags
 * - documentTags: ref({}) - Maps document IDs to their selected tag IDs
 * - tagsOptionsForSelect: computed - Formats tags for multiselect component
 * 
 * NAMING CONVENTIONS:
 * - Functions starting with 'api' are internal API calls
 * - Functions without 'api' prefix are public functions that may include state updates
 * - 'removeTagFromDoc' vs 'apiRemoveTagFromDocument': The first updates UI state, the second is just the API call
 */

const tags = ref([]);
const documentTags = ref({}); // Store tags for each document: { documentId: [tagIds] }
const openDropdownId = ref(null); // Track which dropdown is currently open

const { translate, translateChoice, ucFirst } = useLang();
const { failToast, successToast } = useToast();
const { apiGet, apiPost, apiDel } = useApi();

export default function useDocumentTags() {

    /**
     * Sets a dropdown as open and ensures all others are closed
     * @param {number} documentId - The ID of the document whose dropdown should be open
     */
    const setDropdownOpen = (documentId) => {
        openDropdownId.value = documentId;
    };

    /**
     * Closes a specific dropdown
     * @param {number} documentId - The ID of the document whose dropdown should be closed
     */
    const setDropdownClosed = (documentId) => {
        if (openDropdownId.value === documentId) {
            openDropdownId.value = null;
        }
    };

    /**
     * Checks if a specific dropdown should be open
     * @param {number} documentId - The ID of the document to check
     * @returns {boolean} True if this dropdown should be open
     */
    const isDropdownOpen = (documentId) => {
        return openDropdownId.value === documentId;
    };

    /**
     * Fetches all available tags from the API
     * @returns {Promise<void>}
     */
    const getTags = async () => {
        try {
            const response = await apiGet('/api/document-tags');
            tags.value = response.data;
        } catch (error) {
            failToast(`${ ucFirst(translate('generic.loadingtagsfailed')) }: ${ error }`);
        }
    };

    /**
     * Fetches tags for a specific document
     * @param {number} documentId - The ID of the document
     * @returns {Promise<Array>} Array of tag objects
     */
    const getDocumentTags = async (documentId) => {
        try {
            const response = await apiGet(`/api/documents/${ documentId }/tags`);
            return response.data;
        } catch (error) {
            failToast(`${ ucFirst(translate('generic.loadingdocumenttagsfailed')) }: ${ error }`);
            return [];
        }
    };

    /**
     * API call to add a tag to a document
     * @param {number} documentId - The ID of the document
     * @param {string} tagName - The name of the tag to add
     * @returns {Promise<Object>} The created tag object
     */
    const addTagToDocument = async (documentId, tagName) => {
        try {
            const response = await apiPost(`/api/documents/${ documentId }/tags`, {
                tag_name: tagName
            });
            successToast(`${ ucFirst(translateChoice('generic.tags', 1)) } "${ tagName }" ${ translate('generic.addedsuccessfully') }`);
            return response.data.tag;
        } catch (error) {
            failToast(`${ ucFirst(translate('generic.addingtagfailed')) }: ${ error }`);
            throw error;
        }
    };

    /**
     * Internal API call to remove a tag from a document (no state updates)
     * @param {number} documentId - The ID of the document
     * @param {number} tagId - The ID of the tag to remove
     * @returns {Promise<void>}
     */
    const apiRemoveTagFromDocument = async (documentId, tagId) => {
        try {
            await apiDel(`/api/documents/${ documentId }/tags/${ tagId }`);
            successToast(`${ ucFirst(translateChoice('generic.tags', 1)) } ${ translate('generic.removedsuccessfully') }`);
        } catch (error) {
            failToast(`${ ucFirst(translate('generic.removingtagfailed')) }: ${ error }`);
            throw error;
        }
    };

    /**
     * Public function that removes a tag from a document AND updates local state
     * @param {number} documentId - The ID of the document
     * @param {number} tagId - The ID of the tag to remove
     * @param {Object} document - The document object to update
     * @returns {Promise<void>}
     */
    const removeTagFromDoc = async (documentId, tagId, document) => {
        try {
            await apiRemoveTagFromDocument(documentId, tagId);

            // Update the local state
            if (document && document.tags) {
                document.tags = document.tags.filter(tag => tag.id !== tagId);
            }

            // Update the multiselect selection
            if (documentTags.value[documentId]) {
                documentTags.value[documentId] = documentTags.value[documentId].filter(id => id !== tagId);
            }

        } catch (error) {
            console.error('Error removing tag:', error);
        }
    };

    /**
     * Loads tags for multiple documents at once and initializes local state
     * @param {Array} documents - Array of document objects
     * @returns {Promise<void>}
     */
    const initializeDocumentTags = async (documents) => {
        for (const doc of documents) {
            const docTags = await getDocumentTags(doc.id);
            doc.tags = docTags;
            documentTags.value[doc.id] = docTags.map(tag => tag.id);
        }
    };

    /**
     * Handles multiselect changes (add/remove multiple tags)
     * Compares current vs selected tags and performs necessary API calls
     * 
     * NOTE: The multiselect component doesn't tell us "what changed" - it only provides
     * the complete new selection. We must reverse-engineer the changes by comparing:
     * - Current state: document.tags (what the document currently has)
     * - New state: selectedTagIds (what the user selected in the multiselect)
     * 
     * While most user interactions are individual (select/deselect one tag), the multiselect
     * batches these into a single change event, so this function handles any combination
     * of simultaneous additions and removals.
     * 
     * @param {number} documentId - The ID of the document
     * @param {Array} selectedTagIds - Array of selected tag IDs
     * @param {Object} document - The document object to update
     * @returns {Promise<void>}
     */
    const updateDocumentTags = async (documentId, selectedTagIds, document) => {
        if (!document) return;

        // Initialize doc.tags if it doesn't exist
        if (!document.tags) document.tags = [];

        const currentTagIds = document.tags.map(tag => tag.id);

        // Find tags to add (in selected but not in current)
        const tagsToAdd = selectedTagIds.filter(tagId => !currentTagIds.includes(tagId));

        // Find tags to remove (in current but not in selected)
        const tagsToRemove = currentTagIds.filter(tagId => !selectedTagIds.includes(tagId));

        try {
            // Handle new tags (when user types a new tag name)
            for (const tagId of tagsToAdd) {
                if (typeof tagId === 'string' && !tagsOptionsForSelect.value.find(t => t.id === tagId)) {
                    // This is a new tag name, create it
                    const newTag = await addTagToDocument(documentId, tagId);
                    // Add the new tag to our tags list
                    tags.value.push(newTag);
                    // Update the document's tags
                    document.tags.push(newTag);
                    // Update the selected tags to use the new tag's ID instead of the string
                    const index = documentTags.value[documentId].indexOf(tagId);
                    if (index !== -1) {
                        documentTags.value[documentId][index] = newTag.id;
                    }
                } else {
                    // This is an existing tag, just add it
                    const tagName = tagsOptionsForSelect.value.find(t => t.id === tagId)?.label;
                    if (tagName) {
                        const newTag = await addTagToDocument(documentId, tagName);
                        document.tags.push(newTag);
                    }
                }
            }

            // Remove tags
            for (const tagId of tagsToRemove) {
                await apiRemoveTagFromDocument(documentId, tagId);
                document.tags = document.tags.filter(tag => tag.id !== tagId);
            }

        } catch (error) {
            console.error('Error updating document tags:', error);
            // Revert the multiselect to the previous state
            documentTags.value[documentId] = document.tags.map(tag => tag.id);
        }
    };

    /**
     * Creates a new tag via API and adds it to the options list
     * @param {string} tagName - The name of the new tag
     * @returns {Promise<Object>} The created tag object
     */
    const createNewTag = async (tagName) => {
        console.log('>>>> createNewTag', tagName);
        try {
            const response = await apiPost('/api/tags', {
                name: tagName
            });

            const newTag = {
                id: response.data.id,
                name: response.data.name,
                label: response.data.label
            };

            // Refetch all tags to ensure all dropdowns are updated
            await getTags();

            return newTag;
        } catch (error) {
            console.error('Failed to create tag:', error);
            throw error;
        }
    };

    // Computed property for tags options in multiselect format
    const tagsOptionsForSelect = computed(() => {
        return tags.value.map(tag => ({
            id: tag.id,
            label: tag.name
        }));
    });

    return {
        tags,
        documentTags,
        tagsOptionsForSelect,
        openDropdownId,
        getTags,
        getDocumentTags,
        addTagToDocument,
        removeTagFromDoc,
        initializeDocumentTags,
        updateDocumentTags,
        createNewTag,
        setDropdownOpen,
        setDropdownClosed,
        isDropdownOpen,
    };
};
