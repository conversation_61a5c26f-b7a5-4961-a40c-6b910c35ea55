import { computed, ref } from "vue";
import useToast from "./useToast.js";
import useLang from "./useLang.js";
import useDateTime from "./useDateTime.js";
import useApi from "./useApi.js";
import moment from "moment";

const studentGroup = ref({
    id: 0,
    name: ''
});

const editMode = computed(() => {
    return studentGroup.value.id > 0;
});

const course = ref(null);
const students = ref(null);
const appointments = ref('0 / 0');
const appointmentsFuture = ref(0);
const coursesForTargetCourse = ref([]);
const requestedDateForParticipation = ref('');
const { successToast, failToast } = useToast();
const { apiGet, apiPost, apiPut, apiDel } = useApi();

export default function useEditStudentGroup () {
    const { ucFirst, translate } = useLang();
    const { displayDateTime, displayDate } = useDateTime();

    /**
     * Get the requested student group - provided we have an id, i.e. editMode = true
     */
    const getStudentGroup = async () => {
        if (editMode.value) {
            // we're in edit mode
            try {
                // (re-)init the coursesForTargetCourse
                coursesForTargetCourse.value = [];
                //
                const response = await apiGet(`/api/studentgroups/${studentGroup.value.id}`);
                studentGroup.value = response.data.data;
                course.value = response.data.data.course;
                appointments.value = `${response.data.data.future_appointments || 0} / ${response.data.data.appointments || 0}`;
                appointmentsFuture.value = response.data.data.future_appointments || 0;
                students.value = response.data.data.students;
            } catch (err) {
                failToast(ucFirst(translate('generic.errorloadingdata')) + ': ' + err);
            }
        }
    };

    /**
     * in new-mode: create a new student group
     * @returns {Promise<void>}
     */
    const createStudentGroup = async () => {
        // the predefined firstname and date_of_birth turns the student into a student group
        try {
            const data = {
                lastname: studentGroup.value.name
            };
            const response = await apiPost('/api/studentgroups', data);
            // on successful save action -> forward to edit mode
            // where we can fill out the rest of the fields and relations
            if (response.data.id === undefined) {
                failToast(ucFirst(translate('generic.errorsavingdata')) + ': ' + response.data);
                return;
            }
            const studentGroupId = response.data.id;
            window.location.href = `/studentgroups/${studentGroupId}/edit`;
        } catch (err) {
            failToast(ucFirst(translate('generic.errorsavingdata')) + ': ' + err);
        }
    };

    /**
     * update the name of the student group
     * if the function receives properties, it sends them along
     * @returns {Promise<void>}
    */
    const updateStudentGroup = async (properties = {}) => {
        try {
            // Only include lastname in payload if we're not sending other properties
            // or if properties don't already include a lastname
            const payload = { ...properties };
            
            // Only add the name if we're not overriding with properties
            if (Object.keys(properties).length === 0 || !properties.hasOwnProperty('lastname')) {
                payload.lastname = studentGroup.value.name;
            }
            
            await apiPut(`/api/studentgroups/${studentGroup.value.id}`, payload);
            successToast(ucFirst(translate('generic.saved')));
            getStudentGroup();
        } catch (err) {
            failToast(ucFirst(translate('generic.errorsavingdata')) + ': ' + err);
        }
    };

    const saveStudentGroup = async (properties) => {
        if (editMode.value) {
            await updateStudentGroup(properties);
        } else {
            await createStudentGroup();
        }
    };

    /**
     * save the groups new data: add the course
     */
    const addCourseToStudentGroup = async (courseId) => {
        // Only pass the courseId parameter - don't include lastname
        await apiPut(`/api/studentgroups/${studentGroup.value.id}`, { courseId });
    };

    const removeCourseFromStudentGroup = async (courseId) => {
        try {
            await apiDel(`/api/removecoursefromstudentgroup/${studentGroup.value.id}/${courseId}`);
            successToast(ucFirst(translate('generic.deletesuccessful')));
            getStudentGroup();
        } catch (err) {
            failToast(ucFirst(translate('generic.deletefailed')) + ': ' + err);
        }
    };

    /**
     * save the groups new data: add the student
     */
    const addStudentToStudentGroup = async (studentId) => {
        // Only pass the studentId parameter - don't include lastname
        await apiPut(`/api/studentgroups/${studentGroup.value.id}`, { studentId });
        getStudentGroup();
    };

    const removeStudentFromStudentGroup = async (studentId) => {
        try {
            await apiDel(`/api/removestudentfromstudentgroup/${studentGroup.value.id}/${studentId}`);
            successToast(ucFirst(translate('generic.deletesuccessful')));
            getStudentGroup();
        } catch (err) {
            failToast(ucFirst(translate('generic.deletefailed')) + ': ' + err);
        }
    };

    const getCoursesForTargetCourse = async () => {
        if (coursesForTargetCourse.value.length > 0 || !course.value?.id) return; // only once
        try {
            const response = await apiGet(`/api/coursesfortargetcourse/${course.value.id}`);
            coursesForTargetCourse.value = response.data;
        } catch (err) {
            failToast(ucFirst(translate('generic.errorloadingdata')) + ": " + err);
        }
    }

    /**
     * Watch for changes in the students-array
     * Set participation boolean based on start_date and end_date compared to
     * today or a requested date if provided (requestedDateForParticipation)
     */
    const getStudentParticipation = async (student) => {
        // get the courses that lead to the course that the student group is following
        await getCoursesForTargetCourse();
        const targetDate = requestedDateForParticipation.value || moment().format('YYYY-MM-DD');
        // find the course that the student follows that is also in coursesForTargetCourse
        // api already skips courses that are not active (enddate < today or startdate > today)
        const theCourseArray = student.courses.filter(stCourse => {
            return stCourse.id === course.value.id ||
                (coursesForTargetCourse.value.find(c => c === stCourse.id));
        });

        // default to not participating, override when you find 1 that is participating
        let returnObj = {isParticipating: false, start: student.pivot.start_date, end: '', courseIsNotTargetCourse: false};
        theCourseArray.some(theCourse => {
            const participation = getParticipationStatus(theCourse, targetDate, student.pivot.start_date);
            if (participation.isParticipating) {
                returnObj = participation;
                return true;
            }
            return false;
        });
        return returnObj;
    }

    const getParticipationStatus = (theCourse, targetDate, studentParticipationDate) => {
        console.log('getParticipationStatus', theCourse.pivot.start_date, targetDate);
        let participationEndFormatted = '';
        let isParticipating = false;
        const participationStartFormatted = theCourse.pivot.start_date.length > 10
            ? displayDateTime(theCourse.pivot.start_date)
            : displayDate(theCourse.pivot.start_date, false);

        if (theCourse.pivot?.end_date) {
            participationEndFormatted = theCourse.pivot.end_date.length > 10
                ? displayDateTime(theCourse.pivot.end_date)
                : displayDate(theCourse.pivot.end_date, false);
            isParticipating = (moment(targetDate).isSameOrAfter(theCourse.pivot.start_date) &&
                moment(targetDate).isSameOrBefore(theCourse.pivot.end_date));
        } else {
            isParticipating = moment(targetDate).isSameOrAfter(theCourse.pivot.start_date);
        }

        return {
            start: studentParticipationDate,
            end: participationEndFormatted,
            isParticipating: isParticipating,
            // notifies the user the student is following a course that leads
            // to the target course but is not the target course itself
            courseIsNotTargetCourse: (theCourse.id !== course.value.id)
        }
    }

    return {
        addCourseToStudentGroup,
        addStudentToStudentGroup,
        appointments,
        appointmentsFuture,
        course,
        coursesForTargetCourse,
        getCoursesForTargetCourse,
        getStudentGroup,
        getStudentParticipation,
        removeCourseFromStudentGroup,
        removeStudentFromStudentGroup,
        requestedDateForParticipation,
        saveStudentGroup,
        studentGroup,
        students
    };
}
