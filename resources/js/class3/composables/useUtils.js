export default function useUtils() {
    /**
     * No operation - to be used to catch @click on a button that is supposed to NOT submit a form
     * Useful if the button doesn't have a click event, so we can't use @click.prevent.
     * This function is used in those cases: @click.prevent="noop"
     */
    const noop = () => {};

    const uniqueId = () => {
        return '_' + Math.random().toString(36).substring(2, 9);
    };

    const scrollToSection = (sectionId, duration) => {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    };

    /**
     * Remove HTML tags from a text string.
     * Uses native DOM API to strip HTML tags.
     */
    const stripHtml = (input) => {
        if (!input) return '';
        
        const div = document.createElement('div');
        div.innerHTML = input;
        
        let text = div.textContent || div.innerText || '';
        
        // replace ,C and .C with , C and . C resp.
        const regex = /\.|,([^ ])/gm;
        const subst = '. $1';
        return text.replace(regex, subst);
    };

    /**
     * Validate if a string is a valid URL format
     * Must start with http:// or https:// and contain at least one dot
     */
    const isValidUrl = (url) => {
        if (!url || typeof url !== 'string') return false;
        const trimmedUrl = url.trim();
        if (!trimmedUrl) return false;
        
        // Check if URL starts with http:// or https:// and has proper domain structure
        const urlPattern = /^https?:\/\/.+\..+/;
        return urlPattern.test(trimmedUrl);
    };

    return {
        isValidUrl,
        noop,
        scrollToSection,
        stripHtml,
        uniqueId
    };
}
