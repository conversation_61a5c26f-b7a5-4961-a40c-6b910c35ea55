import { ref, computed } from 'vue';
import useApi from './useApi.js';
import useToast from './useToast.js';
import useLang from './useLang.js';

const { apiGet } = useApi();
const { failToast } = useToast();
const { ucFirst, translate } = useLang();

const busy = ref(false);
const registrations = ref([]);
const sortField = ref('name');

export default function useChecklists() {
    const getData = async () => {
        try {
            busy.value = true;
            const response = await apiGet('/api/registrationswithchecklists');
            registrations.value = response.data;
        } catch (error) {
            failToast(error.message);
        } finally {
            busy.value = false;
        }
    };

    /**
     * sort the array of object with one of these compare functions
     * see computed regsNotCompleted and regsCompleted
     * @param a
     * @param b
     */
    const sortRegsName = (a, b) => {
        return a.name < b.name
            ? -1
            : a.name > b.name
                ? 1
                : 0;
    };

    const sortRegsList = (a, b) => {
        return a.checklistname < b.checklistname
            ? -1
            : a.checklistname > b.checklistname
                ? 1
                : 0;
    };

    /**
     * switch sort by which field
     */
    const switchSortField = () => {
        sortField.value = sortField.value === 'name' ? 'list' : 'name';
    };

    const sortByLabel = computed(() => {
        return sortField.value === 'name'
            ? ucFirst(translate('generic.sortbylist'))
            : ucFirst(translate('generic.sortbyname'));
    });

    const regsNotCompleted = computed(() => {
        const filtered = registrations.value.filter(reg => !reg.complete);
        return sortField.value === 'name'
            ? filtered.sort(sortRegsName)
            : filtered.sort(sortRegsList);
    });

    const regsCompleted = computed(() => {
        const filtered = registrations.value.filter(reg => reg.complete);
        return sortField.value === 'name'
            ? filtered.sort(sortRegsName)
            : filtered.sort(sortRegsList);
    });

    return {
        busy,
        getData,
        registrations,
        regsCompleted,
        regsNotCompleted,
        sortByLabel,
        sortField,
        switchSortField
    };
}
