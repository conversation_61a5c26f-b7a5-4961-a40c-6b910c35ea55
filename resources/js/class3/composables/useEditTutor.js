import { ref, watch } from 'vue';
import useToast from './useToast.js';
import useLang from './useLang.js';
import useApi from './useApi.js';

const idToEdit = ref(0);
const tutor = ref(null);
const busy = ref(false);

export default function useEditTutor () {
    const { failToast, successToast } = useToast();
    const { ucFirst, translate } = useLang();
    const { apiGet, apiPost, apiPut, apiDel } = useApi();

    watch(idToEdit, async () => {
        if (!busy.value) {
            if (idToEdit.value === -1) {
                const today = new Date();
                const dd = ('00' + today.getDate()).slice(-2);
                const mm = ('00' + (today.getMonth() + 1)).slice(-2);
                const yyyy = today.getFullYear();
                busy.value = true;
                tutor.value = {
                    id: -1,
                    inUse: false,
                    is_blocked: 0,
                    has_future_events: false,
                    name: '',
                    hexcolor: '',
                    email: '',
                    telephone: '',
                    telephone_extra: null,
                    preferred_language: null,
                    last_active_at: null,
                    start_date: `${yyyy}-${mm}-${dd}`,
                    end_date: null
                };
                busy.value = false;
            } else if (idToEdit.value !== tutor.value?.id) {
                busy.value = true;
                try {
                    const response = await apiGet(`/api/tutors/${idToEdit.value}`);
                    tutor.value = response.data.data;
                } catch (err) {
                    console.log(err);
                } finally {
                    busy.value = false;
                }
            }
        }
    });

    /**
     * save current Tutor data
     * @returns {Promise<void>}
     */
    const saveTutor = async () => {
        try {
            if (tutor.value.id === -1) {
                // POST / NEW
                const response = await apiPost('/api/tutor', tutor.value);
                successToast(
                    ucFirst(translate('generic.savesuccess')),
                    ucFirst(translate('generic.success'))
                );
                window.emitter.emit('updatetutors');
                window.dirty = false;
                tutor.value.id = response.data.newid;
            } else {
                // PUT / UPDATE
                await apiPut(`/api/tutor/${tutor.value?.id}`, tutor.value);
                successToast(
                    ucFirst(translate('generic.savesuccess')),
                    ucFirst(translate('generic.success'))
                );
                window.emitter.emit('updatetutors');
                window.dirty = false;
            }
        } catch (err) {
            failToast(
                ucFirst(translate('generic.savingfailed')) + err,
                ucFirst(translate('generic.fail'))
            );
        }
    };

    const deleteTutor = async (id) => {
        try {
            await apiDel(`/api/tutor/${id}`);
            successToast(
                ucFirst(translate('generic.tutordeleted')),
                ucFirst(translate('generic.success'))
            );
            // on success: refresh list
            window.emitter.emit('updatetutors');
            // if we have this tutor in the edit section: close the edit section
            if (id === tutor.value?.id) {
                tutor.value = null;
            }
        } catch (err) {
            failToast(
                ucFirst(translate('generic.unabletodeleteturtor')),
                ucFirst(translate('generic.deletefailed')) + err
            );
        }
    };

    return {
        busy,
        deleteTutor,
        idToEdit,
        saveTutor,
        tutor
    };
};
