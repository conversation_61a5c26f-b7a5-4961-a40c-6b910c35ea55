import { computed, ref, watch } from 'vue';
import useBaseData from './useBaseData.js';
import useDateTime from './useDateTime.js';
import useToast from './useToast.js';
import useLang from './useLang.js';
import useApi from './useApi.js';

const { currentOrFutureSchoolYear, initBaseData } = useBaseData();
const { failToast, successToast } = useToast();
const { convertToMysqlDateTime } = useDateTime();
const { ucFirst, translate } = useLang();
const { apiGet, apiPost, apiDel } = useApi();

const busyPlanning = ref(false); // is the component busy with loading data?
const student = ref(null); // the data of the student that we are going to plan for
const studentGroup = ref(null); // the group of the students that we are going to plan for
const course = ref(null); // the selected course that we are going to plan or that is already planned
const courseRegistration = ref(null); // the course registration of the student for the selected course
const events = ref([]); // the appointments of the student for the selected course
const eventsToPersist = ref([]); // the appointments of the student for the selected course that are to be persisted
const eventsSummary = ref([]); // summary of actual and blocked events
const persisting = ref(false); // is the component busy with persisting data?
const activeSchoolYear = ref(null); // the school year that is currently active for the planning
const analysing = ref(false); // is the component busy with analysing data?
const analysisResult = ref(null);
const relevantDateExceptions = ref([]); // the relevant date exceptions for the current school year

export default function usePlanning() {
    watch(course, () => {
        initBaseData({
            schoolYears: true
        }).then(() => {
            getEventsOfRegistrationForSchoolYear();
            getRelevantDateExceptions();
        });
    });


    const getEventsOfRegistrationForSchoolYear = async () => {
        if (course.value?.pivot && currentOrFutureSchoolYear.value?.id) {
            courseRegistration.value = course.value.pivot;
            // get events of the student for the selected course
            try {
                busyPlanning.value = true;
                const response = await apiGet('/api/eventsreginsy', {
                    params: {
                        registrationId: courseRegistration.value.id,
                        schoolYearId: currentOrFutureSchoolYear.value.id,
                        onlySummary: false
                    }
                });
                events.value = response.data.events;
                eventsSummary.value = response.data.summary;
                busyPlanning.value = false;
            } catch (e) {
                failToast(e);
                busyPlanning.value = false;
            }
        } else {
            console.log('no course or school year');
            console.log("course: ", course.value);
            console.log("school year: ", currentOrFutureSchoolYear.value);
        }
    };

    const renderStudent = (student) => `<li class="list-group-item"><a href="/students/${student.id}/edit">${student.name}</a></li>`;

    const renderStudentList = (students, columnsWidth = 12) => {
        const studentHtml = students.map(renderStudent).join('');
        return `
      <div class="col-${columnsWidth}">
        <ul class="list-group list-group-flush">
          ${studentHtml}
        </ul>
      </div>
    `;
    };

    const getRelevantDateExceptions = async () => {
        if (course.value?.pivot && currentOrFutureSchoolYear.value?.id) {
            try {
                const response = await apiGet(`/api/dateexceptions/forschoolyear/${currentOrFutureSchoolYear.value.id}`);
                relevantDateExceptions.value = response.data.data;
            } catch (error) {
                relevantDateExceptions.value = [];
                failToast(error);
            }
        }
    }

    const getSummaryOfRegEventsForSY = async (regId = 0, schoolYearId = 0) => {
        const response = await apiGet('/api/eventsreginsy', {
            params: {
                registrationId: regId,
                schoolYearId: schoolYearId,
                onlySummary: true
            }
        });
        return response.data.summary;
    }

    const currentDateExceptions = computed(() => {
        return relevantDateExceptions.value?.map((dateException) => {
            return {
                id: dateException.id,
                datetime_start: dateException.datetime_start,
                datetime_end: dateException.datetime_end,
                reason: dateException.reason,
                plan_blocking: dateException.plan_blocking,
                isWholeDay: dateException.isWholeDay
            };
        });
    });


    const listStudents = (students) => {
        let result;
        if (students.length > 24) {
            const column1 = students.slice(0, 12);
            const column2 = students.slice(12, 24);
            const column3 = students.slice(24);
            result = `<div class="row">${renderStudentList(column1, 4)} ${renderStudentList(column2, 4)} ${renderStudentList(column3, 4)}</div>`;
        } else if (students.length > 12) {
            const column1 = students.slice(0, 12);
            const column2 = students.slice(12);
            result = `<div class="row">${renderStudentList(column1, 6)} ${renderStudentList(column2, 6)}</div>`;
        } else {
            result = `<div class="row">${renderStudentList(students)}</div>`;
        }
        return result;
    };

    const analyseData = async (
        date, time, locationId, tutorId, locationIdAlt, repeats
    ) => {
        console.log('analyse');
        // check the validity of props
        if (
            date &&
            time &&
            locationId &&
            tutorId &&
            courseRegistration.value &&
            (student.value || studentGroup.value)
        ) {
            // time should be in format HH:mm
            const confTime = time.split(':');
            // ask backend for analysis
            analysing.value = true;
            analysisResult.value = null;
            const data = {
                date: convertToMysqlDateTime(date.substring(-10)),
                time: `${confTime[0]}:${confTime[1]}`,
                locationId,
                tutorId,
                repeats,
                courseRegistrationId: courseRegistration.value.id
            };
            // student od studentgroup?
            if (student.value) {
                data.studentId = student.value.id;
                data.isStudentGroup = false;
            } else {
                data.studentGroupId = studentGroup.value.id;
                data.isStudentGroup = true;
            }
            // only send if it's an actual location (validation will fail otherwise)
            if (locationIdAlt > 0) {
                data.locationIdAlt = locationIdAlt;
            }
            try {
                const response = await apiPost('/api/analysePlanRequest', data);
                analysisResult.value = response.data;
                eventsToPersist.value = [];
                // initially, all events that have no blocking severity messages are to be persisted
                const dateTimesAsArray = Object.keys(analysisResult.value);
                Object.values(analysisResult.value).forEach((event, index) => {
                    // check if the event has remarks with 'blocking' severity
                    const hasBlockingRemarks = event.remarks?.filter((remark) => {
                        return remark.severity === 'blocking';
                    }).length > 0;
                    if (!hasBlockingRemarks) {
                        eventsToPersist.value.push(dateTimesAsArray[index]);
                    }
                });
            } catch (e) {
                failToast(e);
            } finally {
                analysing.value = false;
            }
        }
    };

    /**
     * Persist the events that are to be persisted, if any.
     * This function will be called when the enduser decides which events to persist
     * after analysis of the requested data using the create-planning-form
     */
    const persistEvents = async () => {
        const data = {
            eventsToPersist: Object.entries(analysisResult.value).reduce((acc, [eventDateTime, event]) => {
                if (eventsToPersist.value.includes(eventDateTime)) {
                    acc[eventDateTime] = event;
                }
                return acc;
            }, {})
        };
        // add meta data
        data.courseRegistrationId = courseRegistration.value.id;
        data.schoolYearId = currentOrFutureSchoolYear.value.id;
        data.createActionAfterTrialLessonTask = true; // todo

        // check the validity of props
        if (
            data.eventsToPersist
        ) {
            // ask backend to persist events
            persisting.value = true;
            // save the bugger
            try {
                const response = await apiPost('/api/savenewevents', data);
                if (response.status > 299) {
                    failToast('unable to save data: ' + response.statusText);
                } else {
                    successToast(response.data?.message, ucFirst(translate('generic.success')), 5000);
                    analysisResult.value = null;
                    // refresh events from database
                    getEventsOfRegistrationForSchoolYear();
                }
            } catch (err) {
                failToast(`error saving events: ${err}`);
            }
            // close the modal: LessonPlanningPreparePopup
            persisting.value = false;
        } else {
            // should never happen, save button should be disabled
            failToast('no data found to persist');
        }
    };

    /**
     * Delete all future appointment events of the learner's registration for the selected course
     * @returns {Promise<void>}
     */
    const deleteFutureAppointments = async () => {
        try {
            const response = await apiDel('/api/eventsreginsy', {
                params: {
                    registrationId: courseRegistration.value.id,
                    schoolYearId: currentOrFutureSchoolYear.value.id,
                    onlyFuture: true
                }
            });
            events.value = response.data.events;
        } catch (error) {
            failToast(error);
        }
    };
    /**
     * Delete a single event by its id
     * @param eventId
     * @returns {Promise<void>}
     */
    const deleteEventById = async (eventId = 0) => {
        if (eventId === 0) {
            return;
        }
        const response = await apiDel(`/api/eventbyid/${eventId}`);
        if (response.status === 204) {
            getEventsOfRegistrationForSchoolYear();
        }
    };

    const toggleStickyById = async (eventId = 0) => {
        if (eventId === 0 || Number.isInteger(eventId) === false) {
            failToast('invalid event id (1)'); // should ever happen to end user
            return;
        }
        const eventIndex = events.value.findIndex((tutoringEvent) => {
            return tutoringEvent.id === eventId;
        });
        if (eventIndex === -1) {
            failToast('invalid event id (2)'); // should ever happen to end user
            return;
        }

        try {
            const response = await axios.put(`/api/togglestickyevent/${eventId}`);
            if (response.status === 200) {
                // update the single event in the events array
                events.value[eventIndex].flag_sticky = !events.value[eventIndex].flag_sticky;
                successToast(
                    translate("generic.stickyflagupdated"),
                    ucFirst(translate('generic.success')),
                    3000
                );
            }
        } catch (error) {
            failToast(error);
        }
    }

    return {
        activeSchoolYear,
        analyseData,
        analysing,
        analysisResult,
        busyPlanning,
        course,
        courseRegistration,
        currentDateExceptions,
        currentOrFutureSchoolYear,
        deleteEventById,
        deleteFutureAppointments,
        events,
        eventsSummary,
        eventsToPersist,
        getEventsOfRegistrationForSchoolYear,
        getRelevantDateExceptions,
        getSummaryOfRegEventsForSY,
        listStudents,
        persistEvents,
        persisting,
        relevantDateExceptions,
        student,
        studentGroup,
        toggleStickyById
    };
}
