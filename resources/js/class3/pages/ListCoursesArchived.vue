<template>
    <panel
        :busy="busy"
    >
        <template v-slot:title>
            <i class="fas fa-graduation-cap"></i>
            {{ ucFirst(translate('generic.archivedcourses')) }}
            <div><small>{{ ucFirst(translate('generic.explainunarchivedcourses')) }}</small></div>
        </template>
        <div class="row tableheader">
            <div class="col-3">{{ translate('generic.coursename') }} (id)</div>
            <div class="col-3">{{ translate('generic.variant') }}</div>
            <div class="col-6">{{ translateChoice('generic.students', 2) }}</div>
        </div>
        <div class="row tablerow" v-for="(course, index) in allArchivedCourses" :key="index">
            <div class="col-3">
                <a :href="`/courses/${course.id}/edit`">{{ course.name }} ({{ course.id }})</a>
            </div>
            <div class="col-3">
                {{ course.recurrenceoption }}
            </div>
            <div class="col-6">
                <span v-for="student in course.students" :key="student.id" class="me-1">{{ student.name }},</span>
            </div>
        </div>
    </panel>
</template>


<script setup>
import { onMounted, ref } from "vue";
import Panel from "../components/Layout/bs5/Panel4.vue";
import useBaseData from "../composables/useBaseData.js";
import useLang from "../composables/useLang.js";
import useToast from "../composables/useToast.js";
const busy = ref(false);
const { initBaseData, allArchivedCourses } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const { failToast } = useToast();

onMounted(async () => {
    busy.value = true;
    try {
        await initBaseData({
            courses: true
        });
    } catch (error) {
        failToast(error);
    } finally {
        busy.value = false;
    }
});
</script>

<style lang="scss" scoped>
@use '../../../sass/tmpl3/variables' as *;
.tablerow {
    border-bottom: solid 1px gray
}
.tableheader {
    background-color: $classblue;
    color: white;
    font-size: 1.4rem;
}
</style>
