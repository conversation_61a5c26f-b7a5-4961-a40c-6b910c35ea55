<template>
    <panel
        :busy="busy"
    >
        <template v-slot:title>
            <font-awesome-icon icon="users"/>
            {{ ucFirst(translateChoice('generic.studentgroups', 2)) }}
        </template>
        <template v-slot:subtitle>
            <a href='/studentgroups/create' class="btn btn-sm btn-success">
                {{ ucFirst(translate('generic.newstudentgroup')) }}
            </a>
        </template>
        <table class="table table-responsive table-sm">
            <thead>
            <tr>
                <th class="text-center">&nbsp;</th>
                <th>{{ ucFirst(translate('generic.studentgroupname')) }}</th>
                <th>{{ ucFirst(translateChoice('generic.coupledcourses', 1)) }}</th>
                <th>{{ ucFirst(translate('generic.nrofstudentsingroup')) }}</th>
                <th>
                    {{ ucFirst(translate('generic.nrofappointments')) }}
                    <span v-tooltip="translate('generic.explainnrofappointmentsforyear')">
                            <i class="fa fa-info-circle"></i>
                        </span>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="studentGroup in studentGroups" :key="studentGroup.id">
                <td>
                    <a
                        v-tooltip="ucFirst(translate('generic.edit'))"
                        :href="'/studentgroups/'+studentGroup.id + '/edit'"
                        class="btn btn-sm btn-success"
                    >
                        <font-awesome-icon icon="edit"/>
                    </a>
                    <a
                        v-if="deleteable(studentGroup.id)"
                        v-tooltip="ucFirst(translate('generic.delete'))"
                        data-bs-toggle="modal"
                        data-bs-target="#deleteStudentGroupModal"
                        @click="studentGroupToDelete = studentGroup.id"
                        class="btn btn-sm btn-danger"
                    >
                        <font-awesome-icon icon="trash"/>
                    </a>
                </td>
                <td>
                    {{ studentGroup.name }}
                </td>
                <td>
                    <a v-if="studentGroup.course"
                       :href="'/courses/' + studentGroup.course.id + '/edit'">
                        {{ studentGroup.course.name }}
                    </a>
                </td>
                <td>
                    <h4 style="display: inline-block">
                        <VDropdown>
                            <button
                                class="btn btn-secondary"
                                :class="{'btn-success': studentGroup.students.length > 0, 'no-pointer' : studentGroup.students.length === 0}"
                            >
                                <font-awesome-icon icon="info-circle" v-if="studentGroup.students.length > 0"/>
                                {{ studentGroup.students.length }}
                            </button>
                            <template #popper v-if="studentGroup.students.length > 0">
                                <div class="popover-content p-2">
                                    <strong>{{ ucFirst(translateChoice('generic.students', 2)) }}</strong>
                                    <hr>
                                    <div v-html="listStudents(studentGroup.students)"></div>
                                </div>
                            </template>
                        </VDropdown>
                    </h4>
                </td>
                <td>
                    <h4>
                        <span class="badge bg-secondary text-white">
                            {{ studentGroup.appointments }} / {{ studentGroup.future_appointments || 0 }}
                        </span>
                    </h4>
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
    <are-you-sure
        :button-text="ucFirst(translate('generic.deletestudentgroup'))"
        @confirmclicked="deleteStudentGroup"
        modal-id="deleteStudentGroupModal"
    >
        {{ ucFirst(translate("generic.areyousuredeletestudentgroup")) }}
    </are-you-sure>
</template>

<script setup>
import { onMounted } from 'vue';
import Panel from '../components/Layout/bs5/Panel4.vue';
import useLang from '../composables/useLang.js';
import useStudentGroups from "../composables/useStudentGroups.js";
import AreYouSure from "../components/Layout/bs5/AreYouSure4.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, deleteStudentGroup, getStudentGroups, studentGroups, studentGroupToDelete } = useStudentGroups();

onMounted(async () => {
    await getStudentGroups();
});

const deleteable = (stgid) => {
    const theSTG = studentGroups.value.find(stg => stg.id === stgid);
    // count appointments and coupled students. couplet course is not relevant
    return theSTG.appointments === 0 && theSTG.students.length === 0;
};

/**
 * for the popover: list students in the group
 * @param students
 * @returns {string}
 */
const listStudents = (students) => {
    let retString = '<ul class="list-group list-group-flush">';
    students.forEach(student => {
        if (student.pivot.as_trial_student > 0) {
            retString += `<li class="list-group-item"><a href="/students/${ student.id }/edit">${ student.name }</a>
            <i class="fa fa-flag text-info"></i></li>`;
        } else {
            retString += `<li class="list-group-item"><a href="/students/${ student.id }/edit">${ student.name }</a></li>`;
        }
    });
    return `${ retString }</ul>` +
        '<hr>' +
        "<i class='fa fa-flag text-info'></i> = " + ucFirst(translate('generic.triallesson'));
};

</script>

<style scoped>
.set-fix-width {
    width: 3em;
}

.no-pointer {
    pointer-events: none !important;
    cursor: none !important;
}
</style>
