<template>
    <div class="cal-container">
        <!-- Header section -->
        <div class="btn-group">
            <span v-for="location in allLocations"
                  :key="location.id"
                  :class="['vuecal__event location' + location.id, 'me-2', 'p-1', 'pill']"
            >
                <span
                    v-html="location.icon"
                    class="loc-icon-background"
                />

                {{ location.name }}
            </span>
        </div>

        <div class="btn-group">
            <button
                v-for="tutor in allTutors"
                :key="tutor.id"
                type="button"
                :class="['btn', 'tutor' + tutor.id ]"
                @click="toggleColSelect(tutor.id)"
            >
                <span class="badge">
                    <input type="checkbox" class="form-check-input" readonly v-model="selectedTutors" :value="tutor.id"/>
                </span>
                {{ tutor.name }}
            </button>
        </div>

        <div class="btn-group">
            <button class="btn btn-outline-secondary" @click="toggleColSelect('all')">
                <i class="fa fa-check-double"></i>
                {{ ucFirst(translate('generic.all'))}}
            </button>
            <button class="btn btn-outline-secondary" @click="toggleColSelect('none')">
                <i class="fa fa-ban"></i>
                {{ ucFirst(translate('generic.none'))}}
            </button>
        </div>

        <div class="alert alert-success" v-if="selectedTutors.length > 0">
            <label><strong>{{ ucFirst(translate('generic.totalsinthisscreen'))}}</strong></label>
            <ul>
                <li v-for="tutor in totalsOfSelectedTutors" :key="tutor.tutorId">
                    {{ tutor.tutorName }}:
                    {{ tutor.totalTimeMinutes }} {{ translateChoice('generic.minutes', tutor.totalTimeMinutes) }}
                    ({{ minutesToTime(tutor.totalTimeMinutes) }}) {{ translate('generic.ofwhich') }}
                    {{ tutor.totalTimeMinutesInEventsStudentsGroupsOverTwo }}
                    {{ translateChoice('generic.minutes', tutor.totalTimeMinutesInEventsStudentsGroupsOverTwo) }}
                    ({{ minutesToTime(tutor.totalTimeMinutesInEventsStudentsGroupsOverTwo) }})
                    {{ translate('generic.ingroupsoverwostudents') }}
                </li>
            </ul>
        </div>

        <!--  The actual calendar -->
        <vue-cal
            id="vuecal"
            :time-cell-height="timeCellHeight"
            :locale="lang"
            :events="events"
            :time-from="8 * 60"
            :time-step="10"
            :today-button="true"
            :disable-views="['years', 'year']"
            :active-view="defaultView"
            @ready="getEvents"
            @view-change="getEvents"
            :split-days="filteredColumns"
            :sticky-split-labels="true"
            :on-event-click="editEvent"
        >
            <template v-slot:time-cell="{ hours, minutes }">
                <div :class="{ 'vuecal__time-cell-line': true, hours: !minutes }">
                    <strong v-if="!minutes" style="font-size: 18px">{{ hours }}:00</strong>
                    <span v-else style="font-size: 11px">{{ minutes }}</span>
                </div>
            </template>
        </vue-cal>

        <!-- POPUPs -->
        <EditEventC3
            @updateCalendarEvents="updateCalendarEvents"
        ></EditEventC3>
        <AreYouSure
            :button-text    = "ucFirst(translate('generic.jumpto')) + ' ' + translateChoice('generic.dateexceptions', 1)"
            modal-id        = "confirm-jump-to-page"
            @confirmclicked = "jumpToPage"
            :confirmText    = "ucFirst(translate('generic.jumptopage', {target: translateChoice('generic.dateexceptions', 2)}))"
        >
        </AreYouSure>
    </div>
</template>

<script setup>
/* https://antoniandre.github.io/vue-cal/ */
import VueCal from 'vue-cal';
import 'vue-cal/dist/i18n/nl.es.js'; // 'en' is available by default
import 'vue-cal/dist/vuecal.css';
import { computed, onMounted, ref, watch } from 'vue';
import useBaseData from '../composables/useBaseData.js';
import useLang from '../composables/useLang.js';
import useEditEvents from '../composables/useEditEvent.js';
import EditEventC3 from '../components/Planning/EditEventC3.vue';
import AreYouSure from '../components/Layout/bs5/AreYouSure4.vue';
import Color from 'color';
import useColor from '../composables/useColor.js';
import useApi from '../composables/useApi.js';
import useToast from '../composables/useToast.js';

const defaultView = ref('week');
const { lang, translate, translateChoice, ucFirst } = useLang();
const { initBaseData, allLocations, allTutors } = useBaseData();
const { getContrastColor } = useColor();
const { apiGet } = useApi();
const { failToast } = useToast();
const splitDays = ref([]);
const events = ref([]);
const selectedTutors = ref([]);
const { setEventToEdit } = useEditEvents();
const timeCellHeight = ref(26);
let currentStartDate = null;
let currentEndDate = null;
let jumpToDateExceptionId = 0;

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true
    });
    updateCalendarEvents();
});

/**
 * only after the tutors have been fetched fill split-days
 */
watch(allTutors, () => {
    selectedTutors.value = [];
    splitDays.value = [];
    for (const tutor of allTutors.value) {
        splitDays.value.push(
            {
                id: tutor.id,
                class: 'tutor' + tutor.id,
                label: tutor.name.match(/[A-Z]+/g).join('')
            }
        );
    }
});

const filteredColumns = computed(() => {
    return splitDays.value.filter(col => selectedTutors.value.includes(col.id));
});

/**
 * (de-)select a tutor's column
 * tutorId may also be a one of {all|none}
 * @type {toggleColSelect}
 * @param tutorId {String|Number}
 */
const toggleColSelect = tutorId => {
    if (typeof tutorId === 'string') {
        if (tutorId === 'all') {
            selectedTutors.value = [];
            for (const tutor of allTutors.value) {
                // set default as 'selected'
                selectedTutors.value.push(tutor.id);
            }
        }
        if (tutorId === 'none') {
            selectedTutors.value = [];
        }
    } else {
        if (selectedTutors.value.includes(tutorId)) {
            // toggle: remove
            selectedTutors.value = selectedTutors.value.filter(item => item !== tutorId);
        } else {
            // toggle add
            selectedTutors.value.push(tutorId);
        }
    }
};

/**
 * Get the total time of all events for every selected tutor
 * @type {ComputedRef<{tutorId: *, tutorName: *, totalNrOfEvents: number, totalTimeMinutes: *}[]>}
 */
const totalsOfSelectedTutors = computed(() => {
    return selectedTutors.value.map(tutorId => {
        return {
            tutorId,
            tutorName: allTutors.value.find(tutor => tutor.id === tutorId).name,
            totalNrOfEvents: events.value.filter(event => event.tutorId === tutorId).length,
            totalTimeMinutes: events.value
                .filter(event => event.tutorId === tutorId)
                .reduce((total, event) => {
                    return total + event.timespan.split(' ')[0] * 1;
                }, 0),
            totalTimeMinutesInEventsStudentsGroupsOverTwo: events.value
                .filter(event => event.tutorId === tutorId && event.students.length > 2)
                .reduce((total, event) => {
                    return total + event.timespan.split(' ')[0] * 1;
                }, 0)
        };
    });
});

const minutesToTime = (minutes) => {
    if (minutes === 0) {
        return '0:00';
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours + ':' + mins;
};

const getEvents = async ({ startDate, endDate }) => {
    currentStartDate = startDate;
    currentEndDate = endDate;
    // reformat dates for the API: yyyy-mm-dd
    const start = startDate.toLocaleDateString('en', { year: 'numeric' }) + '-' +
        startDate.toLocaleDateString('en', { month: '2-digit' }) + '-' +
        startDate.toLocaleDateString('en', { day: '2-digit' });
    const end = endDate.toLocaleDateString('en', { year: 'numeric' }) + '-' +
        endDate.toLocaleDateString('en', { month: '2-digit' }) + '-' +
        endDate.toLocaleDateString('en', { day: '2-digit' });
    try {
        const response = await apiGet(`api/allcalendarevents?start=${start}&end=${end}`);
        events.value = response.data;
        scrollToCurrentTime();
    } catch (error) {
        failToast(error.message);
    }
};

const scrollToCurrentTime = () => {
    const now = new Date();
    const calendar = document.querySelector('#vuecal .vuecal__bg');
    const hours = now.getHours() + now.getMinutes() / 60;
    calendar.scrollTo({ top: hours * timeCellHeight.value, behavior: 'smooth' });
};

/**
 * User clicked on a cell either to
 * - edit an existing event
 * - navigate to the student's card
 * - navigate to the date exception
 * @param event the calendar event
 * @param e the click event
 */
const editEvent = (event, e) => {
    // did the user click right on the icon, or on the span surrounding it?
    let studentId = e.target.parentNode.getAttribute('data-student-id');
    if (studentId == null) {
        studentId = e.target.getAttribute('data-student-id');
    }
    // if studentId is not null the user wants to navigate to the student's card
    if (studentId != null) {
        window.location.href = `/students/${studentId}/edit`;
        return;
    }

    if (event.eventType === 'dateException') {
        // for now: link to edit dateexception
        jumpToDateExceptionId = event.id;
        const modal = new bootstrap.Modal(document.getElementById('confirm-jump-to-page'));
        modal.show();
    } else {
        // save ref to the event that was clicked,
        // will be used in the popup that follows
        setEventToEdit(event);
        // open edit dialog
        const modal = new bootstrap.Modal(document.getElementById('editEvent'));
        modal.show();
    }
};

const jumpToPage = () => {
    window.location.href = `/dateexceptions?deid=${jumpToDateExceptionId}`;
};

/**
 * This function is used after interaction by the end user with the calendar view
 * The get events function needs the start date and end date
 * After the first rendering (@ready on vue-cal) these
 * are available as variables
 */
const updateCalendarEvents = () => {
    getEvents({
        startDate: currentStartDate,
        endDate: currentEndDate
    });
};

const tutorColors = computed(() => {
    return allTutors.value.reduce((acc, tutor) => {
        acc[tutor.id] = tutor.hexcolor;
        return acc;
    }, {});
});

watch(tutorColors, (value, oldValue) => {
    if (!tutorColors.value || preventDoubleGenerateTutorColors) {
        return '';
    }
    preventDoubleGenerateTutorColors = true;
    let styleString = '';
    for (const key in tutorColors.value) {
        const hexColor = tutorColors.value[key];
        let colorLight = new Color(hexColor).lighten(0.9).rgb().array().join(',');
        let transp = 0.3;
        // if the color is already very light, use the original color otherwise the lightened color will be 'white'
        if (colorLight === '255,255,255') {
            colorLight = Color(hexColor).rgb().array().join(',');
            transp = 0.1;
        }
        const textColor = getContrastColor(hexColor);
        styleString += `.btn.tutor${key} { background-color: ${hexColor}; color: ${textColor} }`;
        styleString += `.vuecal__cell-split.tutor${key} { border-right:solid 1px ${hexColor};background-color: rgba(${colorLight},${transp}); color: ${textColor}; }`;
    }
    createStyleSheet(styleString);
});

let preventDoubleGenerateDateExceptionColors = false;
watch(events, (value, oldValue) => {
    if (!events.value || preventDoubleGenerateDateExceptionColors) {
        return '';
    }
    preventDoubleGenerateDateExceptionColors = true;
    let styleString = '';
    for (const key in events.value) {
        const event = events.value[key];
        if (event.eventType === 'dateException' && event?.class !== '' && event?.isPlanBlocking === 0) {
            styleString += `.non-blocking_${event.id} { background-color: ${event.backgroundColor}; color: ${getContrastColor(event.backgroundColor)}; }`;
        }
    }
    createStyleSheet(styleString);
});

let preventDoubleGenerateTutorColors = false;

const createStyleSheet = (css) => {
    const head = document.head || document.getElementsByTagName('head')[0];
    const style = document.createElement('style');
    style.type = 'text/css';
    style.appendChild(document.createTextNode(css));
    head.appendChild(style);
};
</script>

<style lang="scss">
@use 'sass:color';

.cal-container {
    height: 900px;
}

/* Location colors */
.vuecal__event {
    &.location1 {
        background-color: color.change(#fd9c42, $alpha: 0.9);
        border: 1px solid #e9882e;
        color: #fff;
    }
    &.location2 {
        background-color: color.change(#a4e6d2, $alpha: 0.9);
        border: 1px solid #90d2be;
        color: black;
    }
}
.pill {
    border-radius: 4px;
    width: fit-content;
    height: 2.5rem;
}
.loc-icon-background {
    background-color: white;
    padding: 1rem 0 0.5rem 0;
}
</style>
