<template>
    <Panel4 :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translateChoice('generic.defaultchecklists', 2)) }}</h3>
        </template>
        <template #subtitle>
            <button
                class="btn btn-primary"
                @click.prevent="emptyDefaultChecklistToEdit"
            >
                {{ ucFirst(translate('generic.newdefaultchecklist')) }}
            </button>
        </template>
        <div class="row">
            <div class="col-xs-12 col-xl-6 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <ListDefaultChecklists />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-xl-6 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <EditDefaultChecklists />
                    </div>
                </div>
            </div>
        </div>
    </Panel4>
</template>

<script setup>
import { onMounted } from "vue";
import Panel4 from "../components/Layout/bs5/Panel4.vue";
import useLang from "../composables/useLang.js";
import useDefaultChecklists from "../composables/useDefaultChecklists.js";
import ListDefaultChecklists from "../components/Students/ListDefaultChecklists.vue";
import EditDefaultChecklists from "../components/Students/EditDefaultChecklists.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, emptyDefaultChecklistToEdit, getDefaultChecklists } = useDefaultChecklists();

onMounted(() => {
    getDefaultChecklists();
});
</script>

<style scoped>

</style>
