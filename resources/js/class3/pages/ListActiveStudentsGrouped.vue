<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('reporting.report_active_students')) }}</h3>
        </template>
        <div class="row">
            <div class="col-12 col-xl-3 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <list-active-students :data="data" />
                    </div>
                </div>
            </div>
            <div class="col-12 col-xl-5 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <graph-active-students :data="data" />
                    </div>
                </div>
            </div>
        </div>        
    </Panel>
</template>

<script setup>
import { ref } from "vue";
import Panel from "../components/Layout/bs5/Panel4.vue";
import useLang from '../composables/useLang.js';
import ListActiveStudents from "../components/Reports/ListActiveStudents.vue";
import GraphActiveStudents from "../components/Reports/GraphActiveStudents.vue";
const { ucFirst, translate } = useLang();

const busy = ref(false);
defineProps({
    data: {
        type: Object,
        required: true
    }
});
</script>
