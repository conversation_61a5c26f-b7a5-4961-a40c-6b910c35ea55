<template>
    <panel>
        <template v-slot:title>
            <i class="fas fa-school"></i>
            {{ ucFirst(translateChoice('generic.courses', 1)) }}
        </template>
        <template v-if="course == null">
            <p>
                {{ ucFirst(translate('generic.nocoursefound'))}}
            </p>
            <choose-course-for-registration
                :button-label="ucFirst(translate('generic.couplecourse'))"
                :popup-title="ucFirst(translate('generic.newcourseregistration'))"
                display-fields="name, groupSize, recurrenceoption"
                api-get-string="/api/courses"
                @record-chosen="addCourseToStudentGroup"
                :exclude-ids="excludeIndividualCourseIds.concat(excludeTrialLessonCourseIds)"
            ></choose-course-for-registration>
        </template>
        <template v-else>
            <table class="table">
                <thead>
                <tr>
                    <th>{{ ucFirst(translate('generic.functions'))}}</th>
                    <th>
                        {{ ucFirst(translate('generic.nrofappointments'))}}
                        <span v-tooltip="ucFirst(translate('generic.explainnrofappointmentsforyear'))">
                            <i class="fa fa-question-circle"></i>
                        </span>
                    </th>
                    <th>{{ ucFirst(translate('generic.coursename'))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <button
                            v-tooltip="translate('generic.delete')"
                            class="btn btn-sm btn-outline-danger"
                            :disabled="!canDeleteCourse"
                            @click="removeCourseFromStudentGroup(course.id)"
                        >
                            <i class="fa fa-trash"></i>
                        </button>
                        <a :href="'/planning/lesson?studentgroupId=' + studentGroup.id + '&courseId=' + course.pivot.course_id"
                           class="btn btn-sm btn-outline-secondary"
                           v-tooltip="ucFirst(translate('generic.opentimetable'))"
                        >
                            <i class="fas fa-calendar"></i>
                        </a>
                    </td>
                    <td>
                        {{ appointments === '0 / 0' ? translate('generic.noappointments') : appointments }}
                    </td>
                    <td>
                        <a :href="'/courses/' + course.id + '/edit'">
                            {{ course.name }}<br>
                            {{ course.recurrenceoption.description }}
                        </a>
                    </td>
                </tr>
                <tr v-if="course">
                    <td>
                        {{ ucFirst(translate('generic.targetgroupsize'))}}
                    </td>
                    <td colspan="2">
                        <span v-if="course.group_size_min === course.group_size_max">
                            {{ course.group_size_min }} {{ translateChoice('generic.students', 2)}}
                        </span>
                        <span v-else-if="course.group_size_max === 1">
                            {{ course.group_size_min }} {{ translateChoice('generic.student', 1)}}
                        </span>
                        <span v-else>
                            {{ course.group_size_min }} - {{ course.group_size_max }} {{ translateChoice('generic.students', 2)}}
                        </span>
                    </td>
                </tr>
                <tr v-if="coursesForTargetCourse.length > 0">
                    <td>
                        <strong class="me-2">{{ ucFirst(translateChoice('generic.courserelationships', 2))}}</strong>
                        <span v-tooltip="translate('generic.explaincourserelationships')">
                            <font-awesome-icon icon="question-circle" />
                        </span>
                    </td>
                    <td colspan="2">
                        <span v-html="relatedCoursesPrintable.join('&nbsp;')"></span>
                    </td>
                </tr>
                </tbody>
            </table>
        </template>
    </panel>
</template>

<script setup>
import {computed, onBeforeMount, watch} from "vue";
import Panel from "../Layout/bs5/Panel4.vue";
import ChooseCourseForRegistration from "../Layout/QuickJump.vue";
import useLang from "../../composables/useLang.js";
import useEditStudentGroup from "../../composables/useEditStudentGroup.js";
import useBaseData from "../../composables/useBaseData.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { ucFirst, translate, translateChoice } = useLang();
const { allCourses, individualCourses, initBaseData } = useBaseData();
const {
    addCourseToStudentGroup,
    appointments,
    appointmentsFuture,
    course,
    coursesForTargetCourse,
    getCoursesForTargetCourse,
    studentGroup,
    students,
    removeCourseFromStudentGroup
} = useEditStudentGroup();

onBeforeMount(async () => {
    await initBaseData({courses: true});
});

watch(course, () => {
    getCoursesForTargetCourse();
}, { immediate: true });

const excludeIndividualCourseIds = computed(() => {
    return individualCourses.value.map(course => course.id);
});
const excludeTrialLessonCourseIds = computed(() => {
    return allCourses.value.filter(course => course.is_trial_course).map(course => course.id);
});

const canDeleteCourse = computed(() => (appointmentsFuture.value === 0 && students.value.length === 0));

// coursesForTargetCourse are just the id's of the courses, not the full course objects
// look them up in the courses-array and return a printable string of the names
const relatedCoursesPrintable = computed(() => {
    return coursesForTargetCourse.value.map(courseId => {
        const theCourse = allCourses.value.find(course => course.id === courseId);
        if (!theCourse) {
            return '';
        }
        return `<span class="badge bg-primary text-white">${theCourse.name} (${theCourse.recurrenceoption})</span>`;
    });
});
</script>

<style scoped>

</style>
