<template>
    <span
        class="alert alert-sm"
        :class="{
            'alert-warning': !participation.isParticipating,
            'alert-success': participation.isParticipating
        }"
        v-tooltip="{ content: toolTipText, html: true }"
    >
        <span v-if="participation?.end">{{ participation.start }} t/m {{ participation.end }}</span>
        <span v-else>{{ translate('generic.from') }} {{ participation.start }}</span>
        <template v-if="participation.courseIsNotTargetCourse" >
            <span class="text-warning ms-2">
                <font-awesome-icon icon="exclamation-triangle" />
            </span>
        </template>
    </span>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import useEditStudentGroup from "../../composables/useEditStudentGroup.js";
import useLang from "../../composables/useLang.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const {
    getStudentParticipation,
} = useEditStudentGroup();
const { translate } = useLang();

const props = defineProps({
    student: Object,
    targetCourse: Object
});

const participation = ref({
    start: '',
    end: '',
    isParticipating: false,
    courseIsNotTargetCourse: false
});

watch(() => props.student, async () => {
    participation.value = await getStudentParticipation(props.student);
}, { immediate: true });

const toolTipText = computed(() => {
    let text = participation.value.isParticipating
        ? translate('generic.participating')
        : translate('generic.notparticipating');
    if (participation.value.courseIsNotTargetCourse) {
        text += `<br>${translate('generic.explaincoursenottargetcourse')}`;
    }
    return text;
});
</script>

