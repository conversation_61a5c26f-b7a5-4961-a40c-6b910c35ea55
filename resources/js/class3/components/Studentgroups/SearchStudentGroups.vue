<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fas fa-search"></i> {{ucFirst(translate('generic.directsearch'))}} {{translateChoice('generic.studentgroups',2)}}
        </template>

        <div class="form-group">
            <div class="input-group searchbox mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="search-addon"><i class="fas fa-search"></i></span>
                </div>
                <input type="text" class="form-control" :placeholder="translate('generic.coursename')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="studentgroupSearchkey">
            </div>
        </div>
        <div class="row bg-info">
            <div class="col-4"><strong>{{ucFirst(translate('generic.name'))}}</strong></div>
            <div class="col-4 titlediv"><strong>{{ucFirst(translateChoice('generic.courses', 1))}}</strong></div>
            <div class="col-3 titlediv-c2"><strong>{{ucFirst(translateChoice('generic.students', 2))}}</strong></div>
        </div>
        <div class="scrollable-400">
            <div v-for="studentgroup in filteredStudentgroups" :key="studentgroup.id" class="row">
                <div class="col-4 text-truncate"><a :href="'/studentgroup/' + studentgroup.id + '/edit'">{{studentgroup.name}}</a></div>
                <div class="col-5 text-truncate"><a :href="'/course/' + studentgroup.course.id + '/edit'">{{studentgroup.course.name}}</a></div>
                <div class="col-3">{{studentgroup.students.length}}</div>
            </div>
            <div v-if="filteredStudentgroups.length === 0" class="row mt-2">
                <div class="col-12 mt-2">
                    <span class="text-primary">{{ucFirst(translate('generic.nostudentgroupsfound'))}}</span>
                </div>
            </div>
        </div>
    </panel>
</template>

<script>
import Panel from '../Layout/bs5/Panel4.vue';
import useApi from "../../composables/useApi";
import useLang from "../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet } = useApi();

export default {
    name: 'SearchStudentGroups',
    components: { Panel },
    setup() {
        return {
            translate,
            translateChoice,
            ucFirst
        };
    },
    data () {
        return {
            busy: false,
            studentgroupSearchkey: '',
            studentgroups: []
        };
    },
    mounted () {
        this.getStudentgroups();
    },
    methods: {
        getStudentgroups () {
            this.busy = true;
            apiGet('/api/studentgroups')
                .then((response) => {
                    this.studentgroups = response.data.data;
                })
                .catch((err) => {
                    console.log(`error retrieving student groups from database ${err}`);
                })
                .finally(() => {
                    this.busy = false;
                });
        }
    },
    computed: {
        filteredStudentgroups () {
            return this.studentgroups.filter(
                group => group.name.toLowerCase().includes(this.studentgroupSearchkey.toLowerCase())
            );
        }
    }
};
</script>

<style scoped>
    .titlediv {
        padding-left: 8px !important;
    }
    .titlediv-c2 {
        padding-left: 4px !important;
    }
</style>
