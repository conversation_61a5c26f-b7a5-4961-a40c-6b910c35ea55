<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-edit"></i>
            {{ ucFirst(translateChoice('generic.dateexceptions', 2)) }}
        </template>
        <template v-slot:subtitle>
            <div class="input-group">
                <select class="form-select me-2" v-model="chosenYearId">
                    <option
                        v-for="schoolyear in allSchoolYears"
                        :key="schoolyear.id"
                        :value="schoolyear.id"
                    >
                        {{ schoolyear.label }}
                    </option>
                </select>
                <select class="form-select me-2" v-model="tutorId">
                    <option :value="0">{{ ucFirst(translate('generic.showall')) }}</option>
                    <option :value="-1">{{ ucFirst(translate('generic.wholeschool')) }}</option>
                    <option v-for="tutor in allTutors" :key="tutor.id" :value="tutor.id">{{ tutor.name }}</option>
                </select>
                <button
                    class="btn btn-success btn-sm"
                    @click="dateExceptionIdToEdit = 0"
                    data-bs-toggle="modal"
                    data-bs-target="#editdateexception"
                    data-testid="new-date-exception-button"
                >
                    {{ ucFirst(translate('generic.new')) }}
                </button>
            </div>
        </template>
        <table class="table">
            <thead>
            <tr>
                <th v-if="isAdmin">{{ ucFirst(translate('generic.functions')) }}</th>
                <th>
                    {{ ucFirst(translate('generic.color')) }}
                    <span v-tooltip="translate('generic.explaincalendarcolor')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th>{{ ucFirst(translate('generic.reason')) }}</th>
                <th>{{ ucFirst(translate('generic.concerns')) }}</th>
                <th>{{ ucFirst(translate('generic.from')) }}</th>
                <th>{{ ucFirst(translate('generic.untilincluding')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.roomlocation')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.detailurl')) }}</th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.alerts', 2)) }}
                    <span v-tooltip="translate('generic.explainignoreforalerts')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
                <th class="text-center">
                    {{ ucFirst(translateChoice('generic.conflicts', 2)) }}
                    <span v-tooltip="translate('generic.explainConflictsDEEvents')">
                        <i class="fa fa-question-circle text-secondary"></i>
                    </span>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="dateException in filteredDateExceptions" :key="dateException.id">
                <td v-if="isAdmin">
                    <button
                        class="btn btn-success btn-sm"
                        @click="dateExceptionIdToEdit = dateException.id"
                        data-bs-toggle="modal"
                        data-bs-target="#editdateexception"
                    >
                        <i class="fa fa-edit"></i>
                    </button>
                    <button
                        class="btn btn-danger btn-sm"
                        @click="dateExceptionToDelete = dateException.id"
                        data-bs-toggle="modal"
                        data-bs-target="#del_areyousure"
                    >
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td>
                    <span
                        v-if="!dateException.plan_blocking"
                        class="badge p-2"
                        :style="{ backgroundColor: dateException.calendar_color }"
                    >Cal</span>
                </td>
                <td>{{ dateException.reason }}</td>
                <td>
                    <template v-if="dateException.tutors.length !== 0">
                        <span v-for="tutor in dateException.tutors" class="me-1" :key="tutor.id">
                            <span v-if="tutor.pivot.confirmed" v-tooltip="translate('generic.confirmed')">
                                <i class="fa fa-check-circle text-success"></i>
                            </span>
                            <span v-else v-tooltip="translate('generic.notconfirmed')">
                                <i class="fa fa-question-circle text-secondary"></i>
                            </span>
                            {{ tutor.name }}
                        </span>
                    </template>
                    <span v-else>{{ ucFirst(translate('generic.wholeschool')) }}</span>
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_start)
                            : displayDateTime(dateException.datetime_start)
                    }}
                </td>
                <td>
                    {{
                        dateException.isWholeDay
                            ? displayDate(dateException.datetime_end)
                            : displayDateTime(dateException.datetime_end)
                    }}
                </td>
                <td class="text-center">
                    {{ dateException.location ? dateException.location.name : "-" }}
                </td>
                <td class="text-center">
                    <span v-if="dateException.detail_url">
                        <a :href="dateException.detail_url" target="_blank">
                            <i class="fa fa-external-link-alt"></i>
                        </a>
                    </span>
                    <span v-else>-</span>
                </td>
                <td v-if="dateException.exclude_from_alerts" class="text-center">
                    <strong class="text-danger">{{ translate('generic.no') }}</strong>
                </td>
                <td v-else class="text-center">
                    {{ translate('generic.yes') }}
                </td>
                <td class="text-center">
                    <span
                        class="badge p-2"
                        style="cursor: pointer; color:white; position: relative; z-index: 1041"
                        :style="{ backgroundColor: showConflictsForDateException(dateException.id).code }"
                        v-html="showConflictsForDateException(dateException.id).result"
                        @click="(e) => handleConflictClick(lessonConflicts, dateException.id, e)"
                    />
                </td>
            </tr>
            </tbody>
        </table>

        <!-- confirm delete course -->
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletedateexception'))"
            @confirmclicked="delDateException"
            modal-id="del_areyousure"
        ></are-you-sure>

        <edit-date-exception
            modal-id="editdateexception"
            :date-exception-id="dateExceptionIdToEdit"
            :schoolyear-id="chosenYearId"
            @triggerUpdateDEList="updateData"
        ></edit-date-exception>

        <conflicts-side-panel
            :is-open="isSidePanelOpen"
            :conflicts="selectedConflicts"
            :dateException="selectedDateException"
            @close="closeSidePanel"
        ></conflicts-side-panel>
    </panel>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import EditDateException from './EditDateException.vue';
import ConflictsSidePanel from './ConflictsSidePanel.vue';
import useBaseData from '../../composables/useBaseData.js';
import useToast from '../../composables/useToast.js';
import useLang from '../../composables/useLang.js';
import useDateTime from '../../composables/useDateTime.js';
import useDateExceptions from "../../composables/useDateExceptions.js";
import useConflictsSidePanel from "../../composables/useConflictsSidePanel.js";

const { allTutors, allSchoolYears, initBaseData } = useBaseData();
const {
    chosenYearId,
    dateExceptionIdToEdit,
    dateExceptionToDelete,
    getLessonConflicts,
    deleteDateException,
    filteredDateExceptions,
    lessonConflicts,
    showConflictsForDateException,
    tutorId,
} = useDateExceptions();

const {
    handleConflictClick,
    isSidePanelOpen,
    selectedConflicts,
    selectedDateException,
    closeSidePanel
} = useConflictsSidePanel();

const { failToast, successToast } = useToast();
const { displayDate, displayDateTime } = useDateTime();
const { ucFirst, translate, translateChoice } = useLang();

const busy = ref(false);

defineProps({
    isAdmin: Boolean
});

onMounted(async () => {
    await updateData();
    // check if we should open a date exception for edit
    // => url contains valid date exception id, if link is clicked from calendar
    const params = (new URL(document.location)).searchParams;
    const deid = params.get('deid');
    if (deid != null) {
        // open date exception for edit
        dateExceptionIdToEdit.value = parseInt(deid);
        // Bootstrap 5 modal trigger
        const modal = new bootstrap.Modal(document.getElementById('editdateexception'));
        modal.show();
    }
});

const delDateException = async () => {
    try {
        await deleteDateException(dateExceptionToDelete.value);
        await updateData();
        successToast(
            ucFirst(translate('generic.deletesuccessful')),
            ucFirst(translate('generic.success'))
        );
        dateExceptionToDelete.value = 0;
    } catch (error) {
        failToast(
            error.message,
            ucFirst(translate('generic.error'))
        );
    }
};

/**
 * set the first received school year 'active' initially
 * check for a query parameter 'sy' and set the chosen year id accordingly
 * @param {Array} value
 */
watch(allSchoolYears, (value, oldValue) => {
    const params = new URL(document.location).searchParams;
    const syParam = params.get('sy');

    if (syParam != null && value.some(sy => sy.id === parseInt(syParam))) {
        chosenYearId.value = parseInt(syParam);
    } else {
        chosenYearId.value = value.length > 0 ? value[0].id : 0;
    }
});


watch(filteredDateExceptions, async (value, oldValue) => {
    await getLessonConflicts();
});

const updateData = async () => {
    await initBaseData({
        locations: true,
        tutors: true,
        dateExceptions: true,
        schoolYears: true
    }, true);
};
</script>
