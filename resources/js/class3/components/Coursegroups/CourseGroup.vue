<template>
    <CourseGroupGeneric />
    <ListCourseGroupCourses v-if="!newMode" />
    <floating-bar>
        <div class="btn-group" role="group" aria-label="floating-bar-functions">
            <a href='/coursegroups' class="btn btn-secondary">
                <font-awesome-icon icon="list" />
                {{ ucFirst(translate('generic.list')) }}
            </a>
            <button class="btn btn-primary" @click="saveCourseGroupData" :disabled="!isSaveable">
                <font-awesome-icon icon="save" />
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </div>
    </floating-bar>
</template>

<script setup>
import { onMounted } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import CourseGroupGeneric from "./CourseGroupGeneric.vue";
import ListCourseGroupCourses from "./ListCourseGroupCourses.vue";
import useCourseGroups from "../../composables/useCourseGroups.js";
import FloatingBar from "../Layout/bs5/FloatingBarBs5.vue";
import useLang from "../../composables/useLang.js";

const { ucFirst, translate } = useLang();
const { courseGroupId, initCourseGroupData, isSaveable, newMode, saveCourseGroupData } = useCourseGroups();
const props = defineProps({
    courseGroupId: {
        type: Number,
        default: 0
    },
});

onMounted(() => {
    courseGroupId.value = props.courseGroupId;
    initCourseGroupData();
});
</script>

<style scoped>

</style>
