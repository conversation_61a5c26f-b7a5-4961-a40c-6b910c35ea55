<template>
    <Panel>
        <template #title>
            <h3>{{ ucFirst(translate("generic.coursegroup")) }}</h3>
        </template>
        <template #subtitle>
            <small v-html="ucFirst(translate('generic.explaincoursegroup'))"/>
        </template>
        <div class="row">
            <div class="col-6">
                <div class="mb-3">
                    <label for="name" class="form-label">{{ ucFirst(translate("generic.name")) }}</label>
                    <input type="text" class="form-control" id="name" v-model="name" required @keyup="dirty=true">
                </div>
            </div>
            <div class="col-6">
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input
                            id="isTrialGroup"
                            class="form-check-input"
                            type="checkbox"
                            v-model="isTrialGroup"
                            @change="dirty=true"
                        >
                        <label class="form-check-label" for="isTrialGroup">
                            {{ ucFirst(translate('generic.istrialgroup')) }} ➞ {{ isTrialGroup ? translate('generic.yes') : translate('generic.no') }}
                        </label>
                    </div>
                    <small v-if="isTrialGroup" class="text-muted d-block mt-1">
                        {{ ucFirst(translate("generic.explaintrialsettrue")) }}
                    </small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <div class="mb-3">
                    <label for="description" class="form-label">{{ ucFirst(translate("generic.webdescription")) }}</label>
                    <ckeditor
                        v-if="ClassicEditor && editorConfigSimple"
                        v-model="webDescription"
                        :editor="ClassicEditor"
                        :config="editorConfigSimple"
                        @input="dirty=true"
                    />
                </div>
            </div>
            <div class="col-6">&nbsp;</div>
        </div>
    </Panel>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import useConfigItems from '../../composables/useConfigItems.js';
import useLang from "../../composables/useLang.js";
import useCourseGroups from "../../composables/useCourseGroups.js";

const { editorConfigSimple, isLayoutReady } = useConfigItems();
const { ucFirst, translate } = useLang();
const { name, isTrialGroup, webDescription } = useCourseGroups();

const dirty = ref(false);

onMounted(() => {
    isLayoutReady.value = true;
});

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});
</script>

<style>
.ck-editor__editable:not(.ck-editor__nested-editable) {
    min-height: 200px;
}
</style>
