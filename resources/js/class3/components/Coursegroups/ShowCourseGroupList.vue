<template>
    <panel>
        <template v-slot:title>
            <i class="text-success fas fa-list"></i>
            {{ ucFirst(translate('generic.coursegroups')) }}
            <span v-if="titleSmall !== ''">
                <br>
                <small v-html="titleSmall" />
            </span>
        </template>
        <template v-slot:subtitle>
            <a href="/coursegroups/create"
               class="btn btn-success btn-sm">
                {{ ucFirst(translate('generic.newcoursegroup')) }}
            </a>
        </template>
        <table class="table table-responsive table-sm">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.functions')) }}</th>
                <th>{{ ucFirst(translateChoice('generic.name', 1)) }} (#
                    {{ translateChoice('generic.courses', 2) }})
                </th>
                <th># {{ ucFirst(translateChoice('generic.tutors', 2)) }}</th>
                <th>{{ ucFirst(translate('generic.remarks')) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="courseGroup in courseGroups" :key="courseGroup.id">
                <td class="ms-1">
                    <a
                            v-tooltip="translate('generic.edit')"
                            class="btn btn-primary btn-sm"
                            :href="'/coursegroups/' + courseGroup.id + '/edit'"
                    >
                        <i class="fa fa-edit"></i>
                    </a>
                    <!-- only deletable is no courses are attached to this group -->
                    <span
                            v-if="courseGroup.courses.length === 0"
                            v-tooltip="ucFirst(translate('generic.delete'))"
                    >
                        <a
                            :data-courseid='courseGroup.id'
                            @click="setCGIDToDelete(courseGroup.id)"
                            data-bs-toggle="modal"
                            data-bs-target="#confirm-delete-cg"
                            data-role="delete-coursegroup"
                            class="btn btn-danger btn-sm"
                        >
                            <i class="fa fa-trash"></i>
                        </a>
                    </span>
                </td>
                <td>{{ courseGroup.name }} ({{ courseGroup.courses.length }})</td>
                <td>
                    <span
                        :class="[
                            'badge',
                            {'bg-success': courseGroup.tutors.length > 0},
                            {'bg-danger': courseGroup.tutors.length === 0}
                        ]"
                    >
                        {{ courseGroup.tutors.length }}
                    </span>
                    <button class="btn btn-sm btn-primary btn-single-line ms-2"
                            @click="getTutorsForCourseGroup(courseGroup.id)"
                            data-bs-target="#choosetutorpopup"
                            data-bs-toggle="modal"
                    >
                        {{ translate('generic.settutors') }}
                    </button>

                </td>
                <td v-if="courseGroup.is_trial_group === 1">
                    {{ translate('generic.explaintrialsettrue') }}
                </td>
                <td v-else>
                    &nbsp;
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
</template>

<script setup>
import panel from '../Layout/bs5/Panel4.vue';
import useLang from '../../composables/useLang.js';

const { ucFirst, translate, translateChoice } = useLang();
defineProps({
    courseGroups: {
        type: Array,
        required: true
    },
    titleSmall: {
        type: String,
        default: ''
    }
});
const emits = defineEmits(['setCGIDToDelete', 'getTutorsForCourseGroup']);
const setCGIDToDelete = (id) => {
    emits('setCGIDToDelete', id);
};
const getTutorsForCourseGroup = (id) => {
    emits('getTutorsForCourseGroup', id);
};
</script>
