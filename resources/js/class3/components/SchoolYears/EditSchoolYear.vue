<template>
    <Panel>
        <template #title>{{ panelTitle }}</template>
        <div class="row" v-if="!busy && schoolYearToEdit?.label !== undefined">
            <div class="col-4">
                <div class="form-group">
                    <label for="schoolyear_label">{{ ucFirst(translate('generic.label')) }}*</label>
                    <input
                        type="text"
                        class="form-control"
                        id="schoolyear_label"
                        v-model="schoolYearToEdit.label"
                        :placeholder="labelSuggestion"
                        required="required"
                        @keyup="dirty=true"
                    />
                </div>
            </div>
            <div class="col-4">
                <div class="form-group">
                    <label for="schoolyear_start_date">{{ ucFirst(translate('generic.startdate')) }}*</label>
                    <VueDatepicker
                        v-model="schoolYearToEdit.start_date"
                        v-bind="dpOptionsDate"
                        :placeholder="translate('generic.clicktoedit')"
                        @open="dirty=true"
                    />
                </div>
            </div>
            <div class="col-4">
                <div class="form-group">
                    <label for="schoolyear_end_date">{{ ucFirst(translate('generic.enddate')) }}*</label>
                    <VueDatepicker
                        v-model="schoolYearToEdit.end_date"
                        v-bind="dpOptionsDate"
                        :placeholder="translate('generic.clicktoedit')"
                        @open="dirty=true"
                    />
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import Panel from "../Layout/bs5/Panel4.vue";
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

import useSchoolYear from '../../composables/useSchoolYear';
import useLang from "../../composables/useLang";
import useDatePicker from "../../composables/useDatePicker";

const { busy, getSchoolYear, schoolYearIDToEdit, schoolYearToEdit } = useSchoolYear();
const { ucFirst, translate } = useLang();
const { dpOptions: dpOptionsDate } = useDatePicker(true);
const dirty = ref(false);

const panelTitle = computed(() => {
    return schoolYearIDToEdit.value === 0
        ? ucFirst(translate('generic.newschoolyear'))
        : 'Edit School Year';
});

const labelSuggestion = computed(() => {
    const currentYear = new Date().getFullYear();
    return `${translate('generic.suggestion')}: ${currentYear} - ${currentYear + 1}`;
});

watch(schoolYearIDToEdit, () => {
    getSchoolYear();
}, {
    immediate: true
});

watch(dirty, () => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = dirty.value;
});

</script>

<style scoped>

</style>
