<template>
    <panel :busy="busy">
        <template #title>
            <i class="fa fa-check-circle"></i>
            {{ucFirst(translate('generic.checklists'))}}
        </template>
        <table class="table">
            <thead>
            <tr>
                <th>{{ucFirst(translate('generic.functions'))}}</th>
                <th>{{ucFirst(translate('generic.datestart'))}}</th>
                <th>{{ucFirst(translate('generic.name'))}}</th>
                <th>{{ucFirst(translate('generic.status'))}}</th>
            </tr>
            </thead>
            <tbody>
                <tr v-for="(checklist, index) in myChecklists" :key="index">
                    <td>
                        <div class="d-flex gap-2">
                            <button 
                                class="btn btn-sm btn-primary"
                                @click="chosenChecklist = checklist"
                                data-bs-toggle="modal"
                                data-bs-target="#editChecklist"
                                v-tooltip="ucFirst(translate('generic.openchecklist'))"
                            >
                                <i class="fas fa-eye"></i>
                            </button>
                            <button 
                                class="btn btn-sm btn-danger"
                                @click="checklistToDelete = checklist.id"
                                data-bs-toggle="modal"
                                data-bs-target="#delChecklist"
                                v-tooltip="ucFirst(translate('generic.deletechecklist'))"
                            >
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                    <td>{{ displayDateTime(checklist.created_at) }}</td>
                    <td>{{ checklist.name }}</td>
                    <td>
                        <span v-if="checklist.isComplete" class="text-success">{{ucFirst(translate('generic.checklistcompleted'))}}</span>
                        <span v-else class="text-danger">{{ucFirst(translate('generic.checklistincomplete'))}}</span>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="row" v-if="filteredDefaultChecklists.length > 0">
            <div class="col">
                <div class="input-group mb-3">
                    <select class="form-select" v-model="selectedDefaultChecklist">
                        <option
                            v-for="defaultChecklist in filteredDefaultChecklists"
                            :key="defaultChecklist.id"
                            :value="defaultChecklist.id"
                        >
                            {{defaultChecklist.name}}
                        </option>
                    </select>
                    <button class="btn btn-outline-primary" @click="addSelectedChecklist" type="button">
                        <i class="fa fa-plus"></i>
                        {{ucFirst(translate('generic.add'))}}
                    </button>
                </div>
            </div>
        </div>

        <!-- Confirm delete checklist -->
        <are-you-sure
            :button-text="ucFirst(translate('generic.deletechecklist'))"
            @confirmclicked="deleteChecklist"
            modal-id="delChecklist"
        ></are-you-sure>

        <modal
            :closetext="ucFirst(translate('generic.close'))"
            :popup-title="ucFirst(translate('generic.edit')) + ' ' + translate('generic.checklistforthisregistration')"
            modal-id="editChecklist"
            size="large"
        >
            <div class="container">
                <ul class="list-unstyled">
                    <li v-for="(checklistItem, index) in checklistItems" :key="index">
                        <div class="form-check form-switch">
                            <input 
                                class="form-check-input"
                                type="checkbox"
                                :id="'checklistItem' + index"
                                v-model="checklistItem.checked"
                                @change="toggleItemChecked(index, checklistItem.checked)"
                            >
                            <label 
                                class="form-check-label ms-2"
                                :class="{ 'text-success': checklistItem.checked }"
                                :for="'checklistItem' + index"
                            >
                                {{ checklistItem.itemName }}
                            </label>
                        </div>
                    </li>
                </ul>
            </div>
        </modal>
    </panel>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import Modal from '../Layout/bs5/Modal4.vue';
import useLang from "../../composables/useLang.js";
import useApi from "../../composables/useApi.js";
import useDateTime from "../../composables/useDateTime.js";
import useToast from "../../composables/useToast.js";

const { translate, ucFirst } = useLang();
const { apiGet, apiDel, apiPut } = useApi();
const { failToast, successToast } = useToast();
const { displayDateTime } = useDateTime();

const busy = ref(false);
const defaultChecklists = ref([]);
const myChecklists = ref([]);
const selectedDefaultChecklist = ref(0);
const checklistToDelete = ref(0);
const chosenChecklist = ref({});

const props = defineProps({
    registrationId: {
        type: Number,
        required: true
    }
});

onMounted(async () => {
    await getRegistrationChecklists();
    await getDefaultChecklists();
    if (filteredDefaultChecklists.value.length > 0) {
        selectedDefaultChecklist.value = filteredDefaultChecklists.value[0].id;
    }
});

const getDefaultChecklists = async () => {
    try {
        const response = await apiGet('/api/defaultchecklists');
        defaultChecklists.value = response.data;
    } catch (err) {
        failToast(`${translate('generic.error')}: ${err}`, translate('generic.error'));
    }
};

const getRegistrationChecklists = async () => {
    try {
        const response = await apiGet(`/api/checklistsforregistration/${props.registrationId}`);
        myChecklists.value = response.data.data;
        // the checklist togglers expect boolean, but they arrive as '1' or '0'
        myChecklists.value.forEach(checklist => {
            checklist.item1_checked = (checklist.item1_checked === 1);
            checklist.item2_checked = (checklist.item2_checked === 1);
            checklist.item3_checked = (checklist.item3_checked === 1);
            checklist.item4_checked = (checklist.item4_checked === 1);
            checklist.item5_checked = (checklist.item5_checked === 1);
            checklist.item6_checked = (checklist.item6_checked === 1);
            checklist.item7_checked = (checklist.item7_checked === 1);
            checklist.item8_checked = (checklist.item8_checked === 1);
            checklist.item9_checked = (checklist.item9_checked === 1);
            checklist.item10_checked = (checklist.item10_checked === 1);
            checklist.item11_checked = (checklist.item11_checked === 1);
            checklist.item12_checked = (checklist.item12_checked === 1);
        });
    } catch (err) {
        failToast(`${translate('generic.error')}: ${err}`, translate('generic.error'));
    }
};

const addSelectedChecklist = async () => {
    try {
        const response = await apiPut(`/api/addchecklisttoregistration/${selectedDefaultChecklist.value}/${props.registrationId}`);
        myChecklists.value = response.data.data;
    } catch (err) {
        failToast(`${translate('generic.errorsavingdata')}: ${err}`, translate('generic.error'));
    }
};

const deleteChecklist = async () => {
    try {
        const resp = await apiDel(`/api/deletechecklisttoregistration/${checklistToDelete.value}/${props.registrationId}`);
        myChecklists.value = resp.data.data;
        if (filteredDefaultChecklists.value.length > 0) {
            selectedDefaultChecklist.value = filteredDefaultChecklists.value[0].id;
        }
    } catch (err) {
        failToast(`${translate('generic.errorsavingdata')}: ${err}`, translate('generic.error'));
    }
};

const toggleItemChecked = async (index, value) => {
    const data = {
        itemnumber: (index + 1),
        state: value
    };
    try {
        const response = await apiPut(`/api/updatecheckboxforchecklist/${chosenChecklist.value.id}/${props.registrationId}`, data);
        myChecklists.value = response.data.data;
        successToast(translate('generic.datasaved'), translate('generic.success'));
    } catch (error) {
        failToast(`${translate('generic.errorsavingdata')}: ${error}`, translate('generic.error'));
    }
};

const filteredDefaultChecklists = computed(() => {
    return defaultChecklists.value.filter((checklist) => {
        return !myChecklists.value.some(cl => cl.name === checklist.name);
    });
});

const checklistItems = computed(() => {
    const keysArray = Object.keys(chosenChecklist.value);
    const retArr = [];
    keysArray.forEach(key => {
        if (key.includes('item') && !key.includes('checked')) {
            if ((chosenChecklist.value[key] !== null) && (chosenChecklist.value[key] !== '')) {
                retArr.push({
                    itemName: chosenChecklist.value[key],
                    checked: chosenChecklist.value[key + '_checked']
                });
            }
        }
    });
    return retArr;
});
</script>

<style scoped>
.form-check-input {
    cursor: pointer;
}

.form-switch .form-check-input {
    width: 2.5em;
    height: 1.25em;
}
</style>

