<template>
    <panel>
        <template #title>
            <i class="fas fa-book"></i>
            {{ ucFirst(translate('generic.coursedata')) }}
            <small class="ms-2">{{ newMode ? translate("generic.new") : translate("generic.edit") }}</small>
        </template>
        <template v-if="!newMode" #subtitle>
            <QuickJumpCourse
                :exclude-id="parseInt(courseId)"
                :popup-title="ucFirst(translate('generic.quickjumptocourse'))"
                display-fields="name, recurrenceoption"
                api-get-string="/api/courses"
                @record-chosen="jumpToCourse"
            />
        </template>
        <div class="d-flex justify-content-start">
            <div class="form-group me-2">
                <label for="courseName">{{ ucFirst(translate('generic.coursename')) }}*</label>
                <input type="text" class="form-control" id="courseName" v-model="courseName" required @keyup="dirty=true">
            </div>
            <div class="form-group me-2">
                <label for="courseGroup">{{ ucFirst(translate('generic.coursegroup')) }}*</label>
                <select class="form-control" id="courseGroup" v-model="courseGroup" @change="dirty=true">
                    <option value="">{{ ucFirst(translate('generic.select')) }}</option>
                    <option v-for="cg in props.courseGroups" :key="cg.id" :value="cg.id">{{ cg.name }}</option>
                </select>
            </div>
            <div class="form-group mx-3">
                <!-- is trial course switch -->
                <div class="form-check form-switch">
                    <input 
                        class="form-check-input" 
                        type="checkbox" 
                        id="isTrialCourse" 
                        v-model="isTrialCourse"
                        @change="dirty=true"
                    >
                    <label
                        class="form-check-label"
                        for="isTrialCourse"
                        v-tooltip="{content: ucFirst(translate('generic.explainistrialcourse')), html: true }"
                    >
                        {{ isTrialCourse ? translate('generic.isatrialcourse') : translate('generic.isnotatrialcourse') }}
                        <font-awesome-icon icon="question-circle" />
                    </label>
                </div>
            </div>
            <div class="form-group mx-3" v-if="!newMode">
                <!-- archive switch -->
                <div class="form-check form-switch">
                    <input 
                        class="form-check-input" 
                        type="checkbox" 
                        id="archiveCourse" 
                        v-model="archiveCourse"
                        @change="dirty=true"
                    >
                    <label
                        class="form-check-label"
                        for="archiveCourse"
                        v-tooltip="{content: ucFirst(translate('generic.explainarchivecourse')), html: true }"
                    >
                        {{ archiveCourse ? translate('generic.coursearchived') : translate('generic.coursenotarchived') }}
                        <font-awesome-icon icon="question-circle" />
                    </label>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-start mb-3">
            <!-- recurrence option * -->
            <div class="form-group me-2">
                <label for="recurrenceOption">
                    {{ucFirst(translateChoice('generic.recurrenceoptions', 1)) }}*
                </label>
                <select class="form-control" id="recurrenceOption" v-model="recurrenceOption" @change="dirty=true">
                    <option value="">{{ ucFirst(translate('generic.select')) }}</option>
                    <option v-for="ro in props.recurrenceOptions" :key="ro.id" :value="ro.id">{{ ro.name }}</option>
                </select>
            </div>

            <!-- min studentgroup size * -->
            <div class="form-group me-2">
                <label for="minStudentGroupSize">
                    {{ ucFirst(translate('generic.minstudentgroupsize')) }}*
                    <span
                        v-tooltip="{ content: ucFirst(translate('generic.explainminmaxgroupsize')), html: true }"
                    >
                        <font-awesome-icon icon="question-circle" />
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="minStudentGroupSize"
                    v-model="minStudentGroupSize"
                    @keyup="dirty=true"
                >
            </div>
            <!-- max studentgroup size * -->
            <div class="form-group">
                <label for="maxStudentGroupSize">{{ ucFirst(translate('generic.maxstudentgroupsize')) }}*</label>
                <input
                    type="number"
                    class="form-control"
                    id="maxStudentGroupSize"
                    v-model="maxStudentGroupSize"
                    @keyup="dirty=true"
                >
            </div>
        </div>
        <div class="d-flex justify-content-start">
            <!-- price incl tax * -->
            <div class="form-group me-2">
                <label for="priceIncTax">{{ ucFirst(translate('generic.price')) }}
                    {{ translate('generic.priceinvoice') }}*</label>
                <input
                    type="number"
                    class="form-control"
                    id="priceIncTax"
                    v-model="priceIncTax"
                    required
                    @keyup="dirty=true"
                >
            </div>
            <!-- price ex tax (calculated, not stored) -->
            <div class="form-group me-2">
                <label for="priceExTax">
                    {{ ucFirst(translate('generic.price')) }} {{ translate('generic.priceExTax') }}
                    <span v-tooltip="{ content: ucFirst(translate('generic.calculated')), html: true }">
                        <font-awesome-icon icon="question-circle" />
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="priceExTax"
                    :value="priceExTax"
                    disabled>
            </div>
            <!-- price sub-adult * -->
            <div class="form-group me-2">
                <label for="pricePreAdult">
                    {{ ucFirst(translate('generic.price')) }} < {{ props.domain.adultThreshold }}*
                    <span v-tooltip="{ content: ucFirst(translate('generic.explainpricesubadult')), html: true }">
                        <font-awesome-icon icon="question-circle" />
                    </span>
                </label>
                <input
                    type="number"
                    class="form-control"
                    id="pricePreAdult"
                    v-model="pricePreAdult"
                    required
                    @keyup="dirty=true"
                >
            </div>
            <!-- price is per * -->
            <div class="form-group">
                <label for="priceIsPer">{{ ucFirst(translate('generic.priceper')) }}*</label>
                <select class="form-control" id="priceIsPer" v-model="priceIsPer" @change="dirty=true">
                    <option v-for="option in priceIsPerOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                    </option>
                </select>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { computed, ref, watch, onMounted, onUnmounted } from "vue";
import useLang from "../../composables/useLang.js";
import useCourse from "../../composables/useCourse.js";

import Panel from '../Layout/bs5/Panel4.vue';
import QuickJumpCourse from '../Layout/QuickJump.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
const dirty = ref(false);

const { ucFirst, translate, translateChoice } = useLang();
const {
    archiveCourse,
    courseGroup,
    courseId,
    courseName,
    isTrialCourse,
    maxStudentGroupSize,
    minStudentGroupSize,
    newMode,
    priceExTaxCalculated,
    priceIncTax,
    priceIsPer,
    priceIsPerOptions,
    pricePreAdult,
    recurrenceOption
} = useCourse();

const jumpToCourse = (courseId) => {
    window.location = `/courses/${courseId}/edit`;
};

const props = defineProps({
    courseGroups: {
        type: Array,
        required: true,
    },
    domain: {
        type: Object,
        required: true,
    },
    recurrenceOptions: {
        type: Array,
        required: true,
    },
});

const priceExTax = computed(() => {
    priceExTaxCalculated.value = priceIncTax.value > 0 ? (priceIncTax.value / (1 + (props.domain.courseTaxRate / 100))).toFixed(2) : 0;
    return priceExTaxCalculated.value;
});

watch(dirty, () => {
    // Emit a custom event when the dirty state changes
    // This can be listened to by other components
    document.dispatchEvent(new CustomEvent('form-dirty-state-changed', { 
        detail: { isDirty: dirty.value } 
    }));
});

// Set up the beforeunload event handler
onMounted(() => {
    const handleBeforeUnload = (e) => {
        if (dirty.value) {
            // Standard way to show a confirmation dialog when navigating away
            e.preventDefault();
            e.returnValue = '';
        }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up the event listener when the component is unmounted
    onUnmounted(() => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
    });
});

</script>
