<template>
    <panel>
        <template #title>
            <i class="fas fa-users"></i>
            {{ucFirst(translateChoice('generic.studentgroups',2))}}
        </template>
        <template #subtitle>
            <a :href="`/studentgroups/create?course_id=${courseId}`"
               class="btn btn-success btn-sm">
                {{ucFirst(translate('generic.newstudentgroup'))}}
            </a>
        </template>
        <div v-if="studentGroupSpecsOfCourse.length > 0">
            <div class="row">
                <div class="col-md-1"><h5>{{ucFirst(translate('generic.functions'))}}</h5></div>
                <div class="col-md-2"><h5>{{ucFirst(translate('generic.name'))}}</h5></div>
                <div class="col-md-9"><h5>{{ucFirst(translate('generic.numberofstudents'))}}</h5></div>
            </div>
            <div class="row" v-for="studentGroup in studentGroupSpecsOfCourse" :key="studentGroup.id">
                <div class="col-md-1">
                    <VDropdown>
                        <a :href="'/studentgroups/' + studentGroup.id + '/edit'"
                           class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        <template #popper>
                            <div class="popover-content p-2">
                                {{ translate('generic.edit') }}
                            </div>
                        </template>
                    </VDropdown>

                    <!-- planning link -->
                    <VDropdown v-if="studentGroup.students.length > 0">
                        <a :href="'/planning/lesson?studentgroupId=' + studentGroup.id"
                           class="edittimetable btn btn-sm btn-outline-secondary">
                            <span class='fas fa-calendar'></span>
                        </a>
                        <template #popper>
                            <div class="popover-content p-2">
                                {{ ucFirst(translate('generic.opentimetable')) }}
                            </div>
                        </template>
                    </VDropdown>
                </div>
                <div class="col-md-2">{{ studentGroup.lastname }}</div>
                <div class="col-md-9">{{ studentGroup.students.length }}</div>
            </div>
        </div>
        <div v-else class="row">
            <div class="col">{{ucFirst(translate('generic.nostudentgroupsfound'))}}</div>
        </div>
    </panel>
</template>

<script setup>
import useLang from "../../composables/useLang.js";
import useCourse from "../../composables/useCourse.js";
import Panel from "../Layout/bs5/Panel4.vue";

const { translate, translateChoice, ucFirst } = useLang();
const { courseId, studentGroupSpecsOfCourse } = useCourse();

</script>

<style scoped>

</style>
