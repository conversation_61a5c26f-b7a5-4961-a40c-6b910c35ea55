<template>
    <panel :busy="busy" extraClass="min-with-cards-2"  panel-test-id="class-search-courses">
        <template v-slot:title>
            <i class="fas fa-search"></i> {{ucFirst(translate('generic.directsearch'))}} {{translateChoice('generic.courses',2)}}
        </template>
        <template v-slot:subtitle>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="onlyactive" v-model="onlyactive">
                <label class="form-check-label" for="onlyactive">{{ucFirst(translate('generic.onlyactive'))}}</label>
            </div>
        </template>
        <div class="mb-3">
            <div class="input-group searchbox mb-3">
                <span class="input-group-text" id="search-addon"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" :placeholder="translate('generic.coursename')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="courseSearchkey">
            </div>
        </div>

        <div class="table-fix-head">
            <table class="table table-striped table-sm">
                <thead class="table-light">
                <tr>
                    <th>{{ucFirst(translate('generic.name'))}}</th>
                    <th>{{ucFirst(translateChoice('generic.recurrenceoptions', 1))}}</th>
                    <th>{{ucFirst(translateChoice('generic.students', 2))}}</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="course in filteredCourses" :key="course.id">
                    <td><a :href="'/courses/' + course.id + '/edit'">{{course.name}}</a></td>
                    <td class="text-truncate">{{ course.recurrenceoption }}</td>
                    <td>{{ course.currentstudentsarray.length }}</td>
                </tr>
                <tr v-if="filteredCourses.length === 0">
                    <td colspan="3">{{ucFirst(translate('generic.nocoursesfound'))}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </panel>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import useLang from "../../composables/useLang.js";
import useApi from "../../composables/useApi.js";
import useToast from "../../composables/useToast.js";

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet } = useApi();
const { failToast } = useToast();

const busy = ref(false);
const onlyactive = ref(true);
const courseSearchkey = ref('');
const courses = ref([]);

onMounted(() => {
    getCourses();
});

const getCourses = async () => {
    busy.value = true;
    try {
        const response = await apiGet('/api/courses');
        courses.value = response.data.data;
    } catch (err) {
        failToast(ucFirst(translate('generic.errorloadingcourses')) + `: ${err}`);
        console.log(`error retrieving courses from database ${err}`);
    } finally {
        busy.value = false;
    }
};

const filteredCourses = computed(() => {
    if (onlyactive.value) {
        return courses.value.filter(
            course =>
                course.name.toLowerCase().includes(courseSearchkey.value.toLowerCase()) &&
                    course.currentstudentsarray.length > 0
        );
    } else {
        return courses.value.filter(
            course => course.name.toLowerCase().includes(courseSearchkey.value.toLowerCase())
        );
    }
});
</script>

<style scoped lang="scss">
@use '../../../../sass/tmpl3/variables' as *;
.table-fix-head {
    overflow-y: auto;
    height: 20rem;
    & thead th {
        position: sticky;
        top: -3px;
    }
}
</style>
