<template>
    <Panel>
        <template v-slot:title>
            <i class="fas fa-user"></i>
            {{ ucFirst(translate(`generic.${title}`)) }}
        </template>
        <table class="table">
            <thead>
            <tr>
                <th v-if="props.filter === 'active_registration'">
                    {{ ucFirst(translateChoice('generic.studentgroups', 1)) }}?
                </th>
                <th>{{ ucFirst(translate('generic.name')) }}</th>
                <th>{{ ucFirst(translate('generic.age')) }}</th>
                <th>{{ ucFirst(translate('generic.registrationdate')) }}</th>
                <th v-if="props.filter !== 'active_registration'">
                    {{ ucFirst(translate('generic.unregisterdate')) }}
                </th>
                <th v-if="props.filter === 'active_registration'">
                    {{ ucFirst(translate('generic.status')) }}
                </th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="student in filteredStudentsOnCourse" :key="student.id">
                <td v-if="student.group_id > 0 && props.filter === 'active_registration'">
                    <a :href="`/studentgroups/${student.group_id}/edit`">{{ student.groupName }}</a>
                </td>
                <td v-if="student.group_id === 0 && props.filter === 'active_registration'">
                    {{ translate('generic.no') }}
                </td>
                <td><a :href="`/students/${student.id}/edit`">{{ student.name }}</a></td>
                <td>{{ displayAge(student.date_of_birth) }}</td>
                <td>{{ displayDate(student.pivot.start_date) }}</td>
                <td v-if="props.filter !== 'active_registration'">
                    {{ displayDate(student.pivot.end_date) }}
                </td>
                <td v-if="props.filter === 'active_registration'">
                    <span v-if="student.pivot.signed">
                        <i class="fas fa-check"></i>
                        {{ translate('generic.studentsigned') }}
                    </span>
                    <span v-else-if="student.pivot.sign_request_send">
                        <i class="fas fa-question"></i>
                        {{ translate('generic.registrationnotsignedafterrequest') }}
                    </span>
                    <span v-else>
                        <i class="fas fa-times"></i>
                        {{ translate('generic.registrationnotsignrequested') }}
                    </span>
                </td>
            </tr>
            </tbody>
        </table>
    </Panel>
</template>

<script setup>
import { computed, onBeforeMount } from "vue";
import useCourse from "../../composables/useCourse.js";
import useLang from "../../composables/useLang.js";
import useDateTime from "../../composables/useDateTime.js";
import Panel from '../Layout/bs5/Panel4.vue';

const { studentsOnCourse, getStudentgroupSpecsForCourse } = useCourse();
const { ucFirst, translate, translateChoice } = useLang();
const { displayAge, displayDate } = useDateTime();

onBeforeMount(async () => {
    await getStudentgroupSpecsForCourse();
});

const props = defineProps({
    filter: {
        type: String,
        required: true,
        validator: (value) => {
            return ["active_registration", "no_active_registration"].includes(value);
        }
    },
})

const filteredStudentsOnCourse = computed(() => {
    return studentsOnCourse.value.filter(student => {
        const startDate = new Date(student.pivot.start_date);
        const endDate = student.pivot.end_date ? new Date(student.pivot.end_date) : null;
        const today = new Date();

        if (props.filter === "active_registration") {
            return startDate <= today && (!endDate || endDate > today);
        } else {
            return startDate > today || (endDate && endDate < today);
        }
    });
});

const title = computed(() => {
    return props.filter === "active_registration" ? "studentsonthiscourse" : "previousstudentsonthiscourse";
});
</script>

<style scoped>

</style>
