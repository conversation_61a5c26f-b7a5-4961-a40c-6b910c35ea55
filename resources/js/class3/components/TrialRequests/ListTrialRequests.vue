<template>
    <panel>
        <template #title>
            {{ ucFirst(translate('generic.receivedlessonrequests')) }}
        </template>
        <table class="table">
            <thead>
            <tr>
                <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                <th class="text-center">{{ ucFirst(translate('generic.view')) }}</th>
                <th style="min-width: 100px">
                    <a v-if="orderBy !== 'status'" @click.prevent="orderBy = 'status'" style="text-decoration: underline">
                        {{ ucFirst(translate('generic.status'))}}
                    </a>
                    <span v-else>
                        {{ ucFirst(translate('generic.status'))}}
                        <!-- the key attribute forces vue to update (recreate) the span -->
                        <!-- display won't update otherwise -->
                        <span @click="dirAsc = !dirAsc" :key="orderByIcon">
                            <font-awesome-icon :icon="orderByIcon" />
                        </span>
                    </span>
                </th>
                <th style="min-width: 150px">
                    <a v-if="orderBy !== 'created_at'" @click.prevent="orderBy = 'created_at'" style="text-decoration: underline">
                        {{ ucFirst(translate('generic.date'))}}
                    </a>
                    <span v-else>
                        {{ ucFirst(translate('generic.date'))}}
                        <span @click="dirAsc = !dirAsc" :key="orderByIcon">
                            <font-awesome-icon :icon="orderByIcon" />
                        </span>
                    </span>
                </th>
                <th>{{ ucFirst(translate('generic.name'))}}</th>
                <th>{{ ucFirst(translate('generic.email'))}}</th>
                <th>{{ ucFirst(translateChoice('generic.courses', 1)) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="trialrequest in orderedTrialRequests" :key="trialrequest.trialstudent.id">
                <td class="text-center">
                    <button
                            class="btn btn-danger btn-sm"
                            data-bs-toggle="modal"
                            data-bs-target="#confirmDeleteTrialRequest"
                            @click="idToDelete=trialrequest.trialstudent.id"
                    >
                        <font-awesome-icon icon="trash" />
                    </button>
                </td>
                <td class="text-center">
                    <button
                            class="btn btn-success btn-sm"
                            @click="idToView = trialrequest.trialstudent.id"
                    >
                        <font-awesome-icon icon="eye" />
                    </button>
                </td>
                <td>
                    {{ trialrequest.trialstudent.trialrequeststatus.description }}
                    <span
                        v-if="trialrequest.trialstudent.trialrequeststatus.needs_admin_action === 1"
                        class="text-danger"
                        v-tooltip="ucFirst(translate('generic.adminactionneeded'))"
                    >
                        <font-awesome-icon icon="exclamation-circle" />
                    </span>
                </td>
                <td>
                    {{ displayDate(trialrequest.trialstudent.created_at) }}
                </td>
                <td>
                    {{ getFullName(trialrequest.trialstudent) }}
                </td>
                <td>
                    {{ trialrequest.trialstudent?.course?.name ?? "Anders" }}
                </td>
            </tr>
            </tbody>
        </table>
    </panel>
    <are-you-sure
        :button-text    = "ucFirst(translate('generic.deletetrialrequest'))"
        modal-id        = "confirmDeleteTrialRequest"
        @confirmclicked = "deleteTrialRequest"
    ></are-you-sure>
</template>

<script setup>
import { computed, onBeforeMount } from "vue";
import Panel from "../Layout/bs5/Panel4.vue";
import useLang from "../../composables/useLang.js";
import useDateTime from "../../composables/useDateTime.js";
import useEditTrialRequests from "../../composables/useEditTrialRequests.js";
import AreYouSure from "../Layout/bs5/AreYouSure4.vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const {
    deleteTrialRequest,
    dirAsc,
    getAllStatuses,
    getAllTrialRequests,
    getFullName,
    idToDelete,
    idToView,
    orderBy,
    orderedTrialRequests
} = useEditTrialRequests();
const { ucFirst, translate, translateChoice } = useLang();
const { displayDate } = useDateTime();

onBeforeMount(async () => {
    await getAllTrialRequests();
    await getAllStatuses();
});

const orderByIcon = computed(() => {
    return dirAsc.value ? "chevron-circle-down" : "chevron-circle-up";
});
</script>
