<template>
    <button class="btn btn-success"
            data-bs-target="#choosecopysource"
            data-bs-toggle="modal"
            @click.prevent="() => {}"
    >
        {{ucFirst(translate('generic.copydatafromotherstudent'))}}
    </button>

    <Modal :popup-title="ucFirst(translate('generic.searchstudent'))"
           :closetext="ucFirst(translate('generic.close'))"
           modal-id="choosecopysource"
           size="extralarge"
    >
        <template v-if="theStudent == null">
            <div class="row">
                <div class="col-md-12">
                    <label>{{ucFirst(translate('generic.search'))}}:
                        <input class="form-control" id="search" v-model="searchKey">
                    </label>
                </div>
            </div>
            <div class="row" v-if="searchResult.length > 0">
                <div class="col-md-12">
                    <label>
                        {{ucFirst(translateChoice('generic.students', searchResult.length))}}
                        ({{ucFirst(translate('generic.currentcourses'))}})
                    </label>
                    <ul class="list-group">
                        <li v-for="(student, index) in searchResult" :key="index" class="list-group-item">
                            <button
                                class="btn btn-primary btn-sm"
                                :data-student-id="student.id"
                                @click.prevent="chooseStudent"
                            >
                                {{ucFirst(translateChoice('generic.choosestudents', 1))}}
                            </button>
                            {{student.name}} ({{student.currentcoursessarray.length}})
                        </li>
                    </ul>
                </div>
            </div>
            <div class="row" v-else>
                <div class="col-md-12">
                    <p>
                        <i class="fa fa-question-circle me-2"></i>
                        {{ucFirst(translate('generic.searchstudentbypartofname'))}}
                    </p>
                </div>
            </div>
        </template>
        <!-- Show rest of input after source student has been chosen -->
        <template v-else>
            <div class="row">
                <div class="col-md-5">
                    <button
                        class="btn btn-primary btn-sm"
                        @click.prevent="resetSearch"
                    >
                        <i class="fa fa-arrow-left"></i>
                        {{ucFirst(translate('generic.back'))}}
                    </button>
                </div>
            </div>
            <div class="form">
                <div class="row">
                    <div class="col-md-5">
                        <div class="mb-3">
                            <label class="form-label">{{ ucFirst(translate('generic.firstname')) }}</label>
                            <input
                                class="form-control"
                                name="firstname"
                                v-model="newFirstName">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">{{ ucFirst(translate('generic.preposition')) }}</label>
                            <input
                                class="form-control"
                                name="preposition"
                                v-model="newPreposition">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="mb-3">
                            <label class="form-label">{{ ucFirst(translate('generic.lastname')) }} (*)</label>
                            <input
                                ref="lastNameField"
                                id="newLastName"
                                class="form-control"
                                name="newLastName"
                                v-model="newLastName">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-5">
                        <div class="mb-3">
                            <label for="newStudentDateOfBirth" class="form-label">{{ ucFirst(translate('generic.birthdate')) }} (*)</label>
                            <VueDatepicker
                                v-model="newDateOfBirth"
                                v-bind="dpOptionsDate"
                                name="newStudentDateOfBirth"
                                id="newStudentDateOfBirth"
                                autocomplete="off"
                                required
                            ></VueDatepicker>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input
                                id="copyaddressdata"
                                class="form-check-input"
                                type="checkbox"
                                v-model="copyToggleAddress"
                            >
                            <label class="form-check-label" for="copyaddressdata">
                                {{ucFirst(translate('generic.copyaddressdata'))}}
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input
                                id="copycontactdata"
                                class="form-check-input"
                                type="checkbox"
                                v-model="copyToggleContact"
                            >
                            <label class="form-check-label" for="copycontactdata">
                                {{ucFirst(translate('generic.copycontactdata'))}}
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input
                                id="copybankdata"
                                class="form-check-input"
                                type="checkbox"
                                v-model="copyToggleBank"
                            >
                            <label class="form-check-label" for="copybankdata">
                                {{ucFirst(translate('generic.copybankdata'))}}
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check form-switch">
                            <input
                                id="copystudentlistsdata"
                                class="form-check-input"
                                type="checkbox"
                                v-model="copyToggleStudentlists"
                            >
                            <label class="form-check-label" for="copystudentlistsdata">
                                {{ucFirst(translate('generic.copystudentlists'))}}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #okbutton v-if="theStudent != null">
            <button
                class="btn btn-danger btn-ok"
                id="createfromotherstudentsavable"
                data-bs-dismiss="modal"
                @click="createfromotherstudent"
                :disabled="!savable"
            >
                {{ucFirst(translate('generic.createstudentwiththesevalues'))}}
            </button>
        </template>
    </Modal>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import Modal from '../Layout/bs5/Modal4.vue';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import useLang from "../../composables/useLang.js";
import useDatePicker from "../../composables/useDatePicker.js";
import useApi from "../../composables/useApi.js";
import useToast from "../../composables/useToast.js";

// Composables
const { translate, translateChoice, ucFirst } = useLang();
const { dpOptions: dpOptionsDate } = useDatePicker(true);
const { apiGet, apiPost } = useApi();
const { successToast, failToast } = useToast();

// Reactive data
const students = ref([]);
const searchKey = ref('');
const theStudent = ref(null);
const newDateOfBirth = ref('');
const newLastName = ref('');
const newFirstName = ref('');
const newPreposition = ref('');
const copyToggleAddress = ref(true);
const copyToggleBank = ref(true);
const copyToggleStudentlists = ref(true);
const copyToggleContact = ref(true);
const lastNameField = ref(null);

// Computed properties
const savable = computed(() => {
    return newDateOfBirth.value.length === 10 && newLastName.value.length > 1;
});

const searchResult = computed(() => {
    if (searchKey.value.length < 3) {
        return [];
    }
    return students.value
        .filter(student => {
            return student.name.toLowerCase().includes(searchKey.value.toLowerCase()) &&
                student.type === 'individual';
        })
        .slice(0, 4);
});

// Methods
const fetchStudents = async () => {
    try {
        const response = await apiGet('/api/students');
        students.value = response.data.data;
    } catch (error) {
        console.log(error);
        failToast(ucFirst(translate('generic.errorloadingdata')));
    }
};

const chooseStudent = async (e) => {
    theStudent.value = students.value.find(student =>
        student.id === parseInt(e.target.getAttribute('data-student-id'))
    );

    // Wait for the DOM to update
    await nextTick();

    // Initialize the student's lastname and preposition
    newLastName.value = theStudent.value.lastname;
    newPreposition.value = theStudent.value.preposition;
};

const resetSearch = () => {
    theStudent.value = null;
};

const createfromotherstudent = async () => {
    try {
        const data = {
            studentId: theStudent.value.id,
            newLastName: newLastName.value,
            newFirstName: newFirstName.value,
            newPreposition: newPreposition.value,
            newDateOfBirth: newDateOfBirth.value,
            copyToggleAddress: copyToggleAddress.value,
            copyToggleContact: copyToggleContact.value,
            copyToggleBank: copyToggleBank.value,
            copyToggleStudentlists: copyToggleStudentlists.value
        };

        const response = await apiPost('/api/students/newfromotherstudent', data);

        successToast(ucFirst(translate('generic.openingstudentpage')), translate('generic.savesuccess'));

        setTimeout(() => {
            window.location.href = `/students/${response.data.studentId}/edit`;
        }, 5000);
    } catch (error) {
        failToast(ucFirst(translate('generic.errorsavingdata')) + `: ${error}`);
        console.log(error);
    }
};

// Lifecycle
onMounted(() => {
    console.log('newStudentByCopy component mounted');
    fetchStudents();
});
</script>

<style scoped>
</style>
