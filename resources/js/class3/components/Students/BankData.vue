<template>
    <panel :busy="busy" panel-test-id="student-card-bank">
        <template v-slot:title>
            <i class="fas fa-money-bill"></i>
            {{ ucFirst(translate('generic.bankdata')) }}
        </template>
        <template v-slot:subtitle></template>

        <div class="row">
            <div class="col-12 col-md-3">
                <div class="mb-3">
                    <label for="bankaccount_number" class="form-label">
                        {{ ucFirst(translate('generic.bankaccountnumber')) }}
                    </label>
                    <input
                        name="bankaccount_number"
                        type="text"
                        id="bankaccount_number"
                        class="form-control bank_account"
                        v-model="bankaccount_number"
                        @input="dirty = true"
                    >
                </div>
            </div>
            <div class="col-12 col-md-3">
                <div class="mb-3">
                    <label for="bankaccount_name" class="form-label">
                        {{ ucFirst(translate('generic.bankaccountname')) }}
                    </label>
                    <input
                        name="bankaccount_name"
                        id="bankaccount_name"
                        type="text"
                        v-model="bankaccount_name"
                        class="form-control"
                        @input="dirty = true"
                    >
                </div>
            </div>
            <div class="col-12 col-md-3">
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <label class="form-check-label" for="permission_auto_banktransfer">
                            {{ permission_auto_banktransfer
                            ? ucFirst(translate('generic.permissionautobanktransfer'))
                            : ucFirst(translate('generic.nopermissionautobanktransfer'))
                            }}
                        </label><br>
                        <input
                            id="permission_auto_banktransfer"
                            class="form-check-input ms-0 mt-3"
                            name="permission_auto_banktransfer"
                            type="checkbox"
                            v-model="permission_auto_banktransfer"
                            @change="dirty = true"
                        >
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-1" v-if="permission_auto_banktransfer">
                <label class="form-label">{{ ucFirst(translate('generic.refresh')) }}</label><br>
                <!-- <span v-tooltip="ucFirst(translate('generic.createnewmandatenumber'))">
                    <button
                        class="btn btn-sm btn-success mt-1"
                        data-toggle="modal"
                        data-target="#NewMandateNumberPopup"
                        @click.prevent="noop"
                    >
                        <font-awesome-icon icon="sync" />
                    </button>
                </span> -->
                <span>
                    <button 
                        v-tooltip="ucFirst(translate('generic.createnewmandatenumber'))"
                        type="button" 
                        class="btn btn-sm btn-success" 
                        data-bs-toggle="modal" 
                        data-bs-target="#NewMandateNumberPopup"
                        @click.prevent="noop"
                    >
                        <font-awesome-icon icon="sync" />
                    </button>
                </span>
            </div>
            <div class="col-12 col-md-2">
                <div v-if="permission_auto_banktransfer">
                    <label for="mandate_number" class="form-label">
                        {{ ucFirst(translate('generic.mandatenumber')) }}
                    </label>
                    <input
                        name="mandate_number"
                        readonly
                        type="text"
                        v-model="mandate_number"
                        id="mandate_number"
                        class="form-control"
                    >
                </div>
            </div>
        </div>
        <div class="d-flex flex-row" v-if="dirty">
            <div class="alert alert-warning">
                {{ ucFirst(translate('generic.pleasesavechanges')) }}
            </div>
        </div>

        <AreYouSure
            modal-id="NewMandateNumberPopup"
            :confirm-text="ucFirst(translate('generic.explaingeneratemandatenumber'))"
            @confirmclicked="changeMandateNumber"
            :button-text="ucFirst(translate('generic.generate'))"
        ></AreYouSure>
    </panel>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import useApi from '../../composables/useApi.js';
import useToast from '../../composables/useToast.js';
import useLang from "../../composables/useLang.js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { translate, ucFirst } = useLang();
const { apiGet, apiPut } = useApi();
const { successToast, failToast } = useToast();

// Props
const props = defineProps({
    studentid: {
        type: String,
        required: true
    }
});

// Reactive data
const dirty = ref(false);
const busy = ref(false);
const bankaccount_name = ref('');
const bankaccount_number = ref('');
const permission_auto_banktransfer = ref(false);
const mandate_number = ref('');
const showMandateModal = ref(false);

// Methods
const getBankAccountInfo = async () => {
    try {
        busy.value = true;
        const response = await apiGet(`/api/studentbankdata/${props.studentid}`);

        bankaccount_name.value = response.data.data.bankaccount_name;
        bankaccount_number.value = response.data.data.bankaccount_number;
        permission_auto_banktransfer.value = response.data.data.permission_auto_banktransfer === 'Ja';
        mandate_number.value = response.data.data.mandate_number;
    } catch (error) {
        console.log(`Error getting students bank info: ${error}`);
        failToast(`Error getting bank info: ${error}`);
    } finally {
        busy.value = false;
    }
};

const changeMandateNumber = async () => {
    console.log('>>>> changeMandateNumber');
    try {
        busy.value = true;
        const response = await apiPut(`/api/changemandatenumber/${props.studentid}`);

        mandate_number.value = response.data.mandate_number;
        successToast(
            ucFirst(translate('generic.savemandatemumbersuccess')),
            ucFirst(translate('generic.success'))
        );
        showMandateModal.value = false;
    } catch (error) {
        failToast(
            ucFirst(translate('generic.errorupdatingmandatenumber')) + `: ${error}`,
            ucFirst(translate('generic.failed'))
        );
    } finally {
        busy.value = false;
    }
};

// Watchers
watch(dirty, (newValue) => {
    // propagate the dirty flag to 'window' (global) to be able
    // to use it in the window.onbeforeunload handler
    window.dirty = newValue;
});

// Lifecycle
onMounted(() => {
    getBankAccountInfo();
});
</script>

<style scoped>
.bank_account {
    text-transform: uppercase;
}
</style>
