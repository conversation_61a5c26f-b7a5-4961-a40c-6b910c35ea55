<template>
    <panel :busy="busy" panel-test-id="student-card-student-lists">
        <template v-slot:title>
            <i class="fas fa-list"></i>
            {{ ucFirst(translateChoice('generic.studentlists', 2)) }}
        </template>
        <template v-slot:subtitle>
            <a href="/studentlists" class="btn btn-success btn-sm">
                {{ ucFirst(translate('generic.jumpto')) }}: {{ translateChoice('generic.studentlists', 2) }}
            </a>
        </template>
        <student-list-row
            v-for="(studentlist, key) in studentlists"
            :key="key"
            :studentlist="studentlist"
            @changed="updateParticipation"
        />
    </panel>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import StudentListRow from './StudentListRow.vue';
import useLang from "../../composables/useLang";
import useApi from "../../composables/useApi";
import useToast from "../../composables/useToast";

const props = defineProps({
    studentid: {
        type: String,
        required: true
    }
});

const { translate, translateChoice, ucFirst } = useLang();
const { apiGet, apiPut } = useApi();
const { successToast, failToast } = useToast();

const busy = ref(false);
const studentlists = ref([]);

onMounted(() => {
    getStudentStudentLists();
});

const getStudentStudentLists = async () => {
    busy.value = true;
    try {
        const { data } = await apiGet(`/api/studentstudentlists/${props.studentid}`);
        studentlists.value = data.data;
    } catch (err) {
        failToast(`Error retrieving studentlists for student ${props.studentid}: ${err}`);
    } finally {
        busy.value = false;
    }
};

/**
 * Update subscription and re-retrieve the studentlists
 * @param event
 */
const updateParticipation = async (event) => {
    busy.value = true;
    const data = {
        action: event.participating === 'on' ? 'subscribe' : 'unsubscribe',
        studentlistId: event.listid
    };
    console.log(`update participation to ${data.action} for studentlist ${data.studentlistId}`);
    
    try {
        await apiPut(`/api/studentstudentlists/${props.studentid}`, data);
        successToast(ucFirst(translate('generic.savesuccess')));
        await getStudentStudentLists();
    } catch (err) {
        // notify save error
        failToast(ucFirst(translate('generic.savingfailed')) + ` - ${err}`);
    } finally {
        busy.value = false;
    }
};
</script>

<style scoped>
/* No styles needed */
</style>
