<template>
    <panel :busy="loading" panel-test-id="student-card-logbook">
        <template v-slot:title>
            <i class="fas fa-comments"></i>
            {{ucFirst(translate('generic.logbook'))}}
        </template>
        <template v-slot:subtitle>
            <button type="button" v-tooltip="ucFirst(translate('generic.newlogentry'))"
                    class="btn btn-sm btn-success" @click.prevent="setLogentry(null)"
                    data-bs-toggle="modal" data-bs-target="#logbookEntryModal">
                {{ucFirst(translate('generic.new'))}}
            </button>
        </template>

        <!-- only show the table if we have logentries -->
        <table class="table table-striped" v-if="logentries.length > 0">
            <colgroup>
                <col style="width: 15%">
                <col style="width: 30%">
                <col style="width: 30%">
                <col style="width: 25%">
            </colgroup>
            <thead>
                <tr>
                    <th>{{ucFirst(translate('generic.functions'))}}</th>
                    <th>{{ucFirst(translate('generic.datetimecreated'))}}</th>
                    <th>{{ucFirst(translate('generic.datetimeupdated'))}}</th>
                    <th>{{ucFirst(translate('generic.logbookentry'))}}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="logentry in logentries" :key="logentry.id">
                    <td>
                        <div class="form-control-static">
                            <span v-tooltip="ucFirst(translate('generic.delete'))">
                                <span data-bs-toggle="modal"
                                      data-bs-target="#delLogEntry"
                                      class="btn btn-sm btn-outline-danger"
                                      @click.prevent="setLogentryToDelete(logentry)">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </span>
                            <span v-tooltip="ucFirst(translate('generic.openlogentry'))">
                                <span data-bs-toggle="modal"
                                      data-bs-target="#logbookEntryModal"
                                      class="btn btn-sm btn-outline-primary"
                                      @click.prevent="setLogentry(logentry)">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </span>
                        </div>
                    </td>
                    <td>{{displayDateTime(logentry.created_at)}}</td>
                    <td>{{displayDateTime(logentry.updated_at)}}</td>
                    <td><div class="breakoff-text">{{ stripHtml(logentry.entry) }}</div></td>
                </tr>
            </tbody>
        </table>

        <!-- No entries found -->
        <div v-if="logentries.length===0" class="text-warning">{{ucFirst(translate('generic.nologentriesfound'))}}</div>

        <!-- POPUP DELETE -->
        <modal :popup-title = "ucFirst(translate('generic.areyousure'))"
               :closetext   = "ucFirst(translate('generic.cancel'))"
               modal-id     = "delLogEntry">
            <p>
                {{ucFirst(translate('generic.thiscannotbeundone'))}}
            </p>
            <p>
                <strong>{{ucFirst(translate('generic.wewillbedeleting'))}}:</strong><br/>
                <span style="display: inline-block" class="breakoff-text text-danger">
                    {{ stripHtml(logentryToDelete.entry) }}
                </span>
            </p>
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-danger"
                    data-bs-dismiss="modal"
                    @click.prevent="handleDeleteLogentry()"
                >
                    {{ ucFirst(translate('generic.deletethislogentry')) }}
                </button>
            </template>
        </modal>

        <!-- POPUP new / edit -->
        <modal :popup-title="ucFirst(translate('generic.logbookentry'))"
               :closetext="ucFirst(translate('generic.close'))"
               modal-id="logbookEntryModal"
               size="large">

            <div class="row">
                <label v-if="logentry.created_at !== ''" class="col-5">
                    {{ucFirst(translate('generic.datetimecreated'))}}:
                </label>
                <span class="col-7">{{ displayDateTime(logentry.created_at) }}</span>
            </div>
            <div class="row">
                <label v-if="logentry.updated_at !== ''" class="col-5">
                    {{ucFirst(translate('generic.datetimeupdated'))}}:
                </label>
                <span class="col-7">{{ displayDateTime(logentry.updated_at) }}</span>
            </div>
            <hr>
<!--            <ckeditor-->
<!--                v-if="ClassicEditor && configMailTemplate"-->
<!--                :editor="ClassicEditor"-->
<!--                v-model="mailBody"-->
<!--                :config="configMailTemplate"-->
<!--            />-->
            <ckeditor
                v-if="ClassicEditor && editorConfigSimple"
                v-model="logentry.entry"
                :editor="ClassicEditor"
                :config="editorConfigSimple"
            />
            <template v-slot:okbutton>
                <button
                    type="button"
                    class="btn btn-primary"
                    data-bs-dismiss="modal"
                    @click.prevent="handleSaveLogEntry()"
                >
                    {{ ucFirst(translate('generic.savelogentry')) }}
                </button>
            </template>
        </modal>

    </panel>
</template>

<script setup>
import { onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import Modal from '../Layout/bs5/Modal4.vue';
import useStudentLog from '../../composables/useStudentLog.js';
import useLang from "../../composables/useLang.js";
import useDateTime from "../../composables/useDateTime.js";
import useUtils from "../../composables/useUtils.js";
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ClassicEditor } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import useConfigItems from "../../composables/useConfigItems.js";

const { editorConfigSimple, isLayoutReady } = useConfigItems();

const { translate, ucFirst } = useLang();
const { displayDateTime } = useDateTime();
const { stripHtml } = useUtils();

const props = defineProps({
    studentid: {
        type: Number,
        required: true
    }
});

const { 
    deleteLogentry, 
    getLogentries, 
    loading, 
    logentries, 
    logentry, 
    logentryToDelete, 
    saveLogEntry, 
    setLogentry, 
    setLogentryToDelete 
} = useStudentLog();

onMounted(async () => {
    await getLogentries(props.studentid);
    isLayoutReady.value = true;
});

const handleSaveLogEntry = async () => {
    await saveLogEntry(props.studentid);
};

const handleDeleteLogentry = async () => {
    await deleteLogentry(props.studentid);
};
</script>

<style>
.breakoff-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}
</style>
