<template>
    <panel :busy="busy" panel-test-id="student-card-course-data">
        <template v-slot:title>
            <i class="fas fa-school"></i>
            {{ucFirst(translate('generic.coursedata'))}}
        </template>
        <template v-slot:subtitle>
            <choose-course-for-registration
                :button-label="ucFirst(translate('generic.add'))"
                :popup-title="ucFirst(translate('generic.newcourseregistration'))"
                display-fields="name, groupSize, recurrenceoption"
                api-get-string="/api/courses"
                @record-chosen="doChooseCourseForRegistration"
                :exclude-ids="activeCourseIds"
            ></choose-course-for-registration>
        </template>
        <div v-if="courses.length > 0">
            <!---->
            <div v-if="activeCourses.length > 0">
                <p class="h5">
                    {{ucFirst(translate('generic.current'))}} {{translateChoice('generic.courses', activeCourses.length)}}
                </p>
                <div v-for="course in activeCourses" :key="course.id">
                    <student-course-row
                        :registration="course"
                        :userisadmin="userisadmin"
                        :student="student"
                        :domain="domain"
                        @refreshMyData="getCourseData"
                    />
                </div>
            </div>
            <!-- -->
            <hr v-if="inactiveCourses.length > 0 && activeCourses.length > 0" />
            <!-- -->
            <div v-if="inactiveCourses.length > 0">
                <p class="h5" v-html="ucFirst(translateChoice('generic.endedcourses', inactiveCourses.length))"></p>
                <div v-for="course in inactiveCourses" :key="course.id">
                    <student-course-row
                        :registration="course"
                        :is-active="false"
                        :userisadmin="userisadmin"
                        :student="student"
                        :domain="domain"
                        :getRegMetaData="false"
                        @registrationDeleted="deleteRegistrationRow"
                    />
                </div>
            </div>
        </div>
        <div v-else>
            <h5>{{ucFirst(translate('generic.nocoursesfound'))}}</h5>
        </div>
    </panel>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import useToast from "../../composables/useToast.js";
import useLang from "../../composables/useLang.js";
import useApi from "../../composables/useApi.js";
import Panel from '../Layout/bs5/Panel4.vue';
import StudentCourseRow from '../Registrations/StudentCourseRow.vue';
import ChooseCourseForRegistration from '../Layout/QuickJump.vue';
import moment from 'moment';

const { failToast, successToast } = useToast();
const { ucFirst, translate, translateChoice } = useLang();
const { apiGet, apiPut } = useApi();

const busy = ref(false);
const courses = ref([]);

const props = defineProps({
    userisadmin: {
        type: Boolean,
        default: false
    },
    student: {
        type: Object,
        required: true
    },
    domain: {
        type: Object,
        required: true
    }
});

onMounted(() => {
    getCourseData();
});

const getCourseData = async () => {
    busy.value = true;
    try {
        const response = await apiGet(`/api/studentcoursedata/${props.student.id}`);
        courses.value = response.data.data;
    } catch (error) {
        failToast(
            ucFirst(translate('generic.errorfetchingcoursedata')) + `: ${error}`,
            ucFirst(translate('generic.error'))
        );
    } finally {
        busy.value = false;
    }
};

/**
 * couples the chosen course to the student as a new registration
 */
const doChooseCourseForRegistration = async (courseId) => {
    try {
        await apiPut('/api/students/addCourseRegistration', { courseId, studentId: props.student.id });
        // re-fetch registered courses list
        await getCourseData();
        successToast(
            ucFirst(translate('generic.registrationsaved')),
            ucFirst(translate('generic.success'))
        );
    } catch (error) {
        failToast(
            ucFirst(translate('generic.errorsavingregistration')) + `: ${error}`,
            ucFirst(translate('generic.error'))
        );
    }
};

/**
 * Handle event: delete registration (NOT setting end date, but actual delete row!)
 * @param ev
 */
const deleteRegistrationRow = ({ regid }) => {
    // remove this course from the local data. please note type-safety
    courses.value = courses.value.filter(course => parseInt(course.pivot.id) !== parseInt(regid));
    // show success to user
    successToast(ucFirst(translate('generic.registrationdeleted')));
};

const activeCourses = computed(() => {
    const now = moment();
    return courses.value.filter(course => {
        const endDate = moment(course.pivot.end_date);
        return !endDate.isBefore(now);
    });
});

const inactiveCourses = computed(() => {
    const now = moment();
    return courses.value.filter(course => {
        const endDate = moment(course.pivot.end_date);
        return endDate.isBefore(now);
    });
});

/**
 * get id's of courses to be filtered out when coupling a new course
 * @returns {int[]}
 */
const activeCourseIds = computed(() => {
    return activeCourses.value.map(course => course.id);
});

</script>

<style scoped>

</style>
