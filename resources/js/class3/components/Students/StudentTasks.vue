<template>
    <panel :busy="busy" panel-test-id="student-card-student-tasks">
        <template v-slot:title>
            <i class="fas fa-list"></i>
            {{ucFirst(translate('generic.studenttasks'))}}
        </template>
        <template v-slot:subtitle>
            <a href="/tasks" class="btn btn-success btn-sm">
                {{ucFirst(translate('generic.jumpto'))}}: {{translateChoice('generic.tasks', 2)}}
            </a>
        </template>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th scope="col">{{ ucFirst(translate('generic.assignedto')) }}</th>
                        <th scope="col">{{ ucFirst(translate('generic.startdate')) }}</th>
                        <th scope="col">{{ ucFirst(translate('generic.duedate')) }}</th>
                        <th scope="col">{{ ucFirst(translate('generic.tasktype')) }}</th>
                        <th scope="col">{{ ucFirst(translate('generic.status')) }}</th>
                        <th scope="col" class="text-center">{{ ucFirst(translateChoice('generic.actions', 1)) }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(task, key) in tasks" :key="key">
                        <td>
                            {{ task.assigned_user_id > 0 ? shortenName(task.assignedTo.name) : '-' }}
                        </td>
                        <td>
                            {{ displayDate(task.date_opened) }}
                        </td>
                        <td>
                            {{ displayDate(task.date_due) }}
                        </td>
                        <td>
                            <a :href="'/tasks/' + task.id + '/edit'" class="text-decoration-none">
                                {{ translate('generic.' + task.tasktype.description) }}
                            </a>
                            <span v-if="task.remarks" v-tooltip="task.remarks" class="ms-2">
                                <i class="fas fa-info-circle text-info"></i>
                            </span>
                        </td>
                        <td>
                            <i :class="taskIcons[getTaskStatus(task)] + ' me-1'"></i>
                            {{ translate('generic.' + getTaskStatus(task)) }}
                        </td>
                        <td class="text-center">
                            <button
                                v-if="getTaskStatus(task) !== 'closed'"
                                class="btn btn-outline-danger btn-sm"
                                @click="closeTask(task.id)"
                                :disabled="busy"
                            >
                                {{ translate('generic.close') }}
                            </button>
                            <span v-else class="text-muted small">
                                {{ translate('generic.closed') }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Empty state -->
            <div v-if="!busy && tasks.length === 0" class="text-center py-4 text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>{{ translate('generic.notasks') }}</p>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import useToast from '../../composables/useToast';
import useDateTime from '../../composables/useDateTime';
import useLang from '../../composables/useLang';
import useApi from '../../composables/useApi';

const busy = ref(false);
const tasks = ref([]);
const { failToast } = useToast();
const { displayDate } = useDateTime();
const { ucFirst, translate, translateChoice } = useLang();
const { apiGet, apiPut } = useApi();
const taskIcons = {
    notopenyet: 'fas fa-hourglass-start',
    open: 'fas fa-clipboard-list',
    overdue: 'fas fa-exclamation-triangle',
    closed: 'fas fa-check-circle',
    other: 'fas fa-question'
};

const props = defineProps({
    studentId: {
        type: String,
        required: true
    }
});

onMounted(async () => {
    await getStudentTasks();
});

const getStudentTasks = async () => {
    busy.value = true;
    try {
        const { data } = await apiGet(`/api/gettaskforstudent/${props.studentId}`);
        tasks.value = data.data;
    } catch (err) {
        failToast(`Error retrieving studenttasks for student ${props.studentId}: ${err}`);
    } finally {
        busy.value = false;
    }
};

/**
 * Returns the status of a task.
 *
 * @param {Object} task - The task object.
 * @return {string} - The status of the task. Returns one of: "not open yet", "open", "closed", "overdue", "unknown".
 */
const getTaskStatus = (task) => {
    if (task == null) {
        return 'unknown';
    }
    // first check if the end date is filled in
    if (task.date_closed) {
        return 'closed';
    }
    // then check if we have a due date, and if it is in the past
    if (task.date_due && new Date(task.date_due) < new Date()) {
        return 'overdue';
    }
    // check if the task is not open yet
    if (new Date(task.date_opened) > new Date()) {
        return 'notopenyet';
    }
    // check if the task has started
    if (new Date(task.date_opened) < new Date()) {
        return 'open';
    }
    return 'unknown';
};

const closeTask = async (taskId) => {
    busy.value = true;
    try {
        await apiPut(`/api/task/${taskId}/close`);
        await getStudentTasks();
    } catch (err) {
        failToast(`Error closing task ${taskId}: ${err}`);
    } finally {
        busy.value = false;
    }
};

const shortenName = (fullname) => {
    if (fullname == null) {
        return '-';
    }
    const parts = fullname.split(' ');
    return parts[0] + parts[parts.length - 1].charAt(0) + '.';
};

</script>

<style scoped>

</style>
