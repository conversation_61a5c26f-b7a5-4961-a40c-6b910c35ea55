<template>
    <strong>{{ ucFirst(translate('reporting.list_nr_of_active_students')) }}</strong>
    <div v-for="(student, index) in data" :key="index">
        <div>{{ index }}: {{ student.length }}</div>
    </div>
</template>

<script setup>
import useLang from '../../composables/useLang.js';
const { ucFirst, translate } = useLang();

defineProps({
    data: {
        type: Object,
        required: true
    }
});
</script>
