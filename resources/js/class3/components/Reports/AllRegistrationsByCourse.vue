<template>
    <div v-if="registrationsForCourses">
        <strong>{{ ucFirst(translate('reporting.all_registrations_by_course')) }}</strong>
        <div class="d-flex flex-wrap mb-3">
            <div class="me-5 mb-2">
                <label class="form-label me-2">{{ ucFirst(translate('generic.totalnrofstudents')) }}</label>
                <span>{{ registrationsForCourses["total-students"] }}</span>
            </div>
            <div class="mb-2">
                <label class="form-label me-2">{{ ucFirst(translate('generic.totalnrofcourses')) }}</label>
                <span>{{ registrationsForCourses["total-courses"] }}</span>
            </div>
        </div>
        <hr>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>{{ ucFirst(translate('generic.course')) }}</th>
                        <th>{{ ucFirst(translate('generic.nrofregistrations')) }}</th>
                        <th>{{ ucFirst(translate('generic.unregistered')) }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(course, index) in registrationsForCourses.courses" :key="index">
                        <td>
                            <strong>{{ course.name }}</strong>
                            {{ course.recurrenceoption.description }}
                        </td>
                        <td>{{ course.nstudents }}</td>
                        <td>{{ course.unregistered }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup>
import useLang from "../../composables/useLang.js";
import useRegistrations from "../../composables/useRegistrations.js";
const { registrationsForCourses } = useRegistrations();
const { ucFirst, translate } = useLang();
</script>

<style scoped>
</style>
