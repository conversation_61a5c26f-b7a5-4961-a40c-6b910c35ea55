<template>
    <div
        class="modal fade"
        :id="modalId"
        tabindex="-1"
        :aria-labelledby="`ModalLabel${modalId}`"
        aria-hidden="true"
    >
        <div
            :class="[modalSize, 'modal-dialog modal-dialog-centered modal-dialog-scrollable']"
            role="document"
        >
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"
                        :id="`ModalLabel${modalId}`"
                    >
                        {{ popupTitle }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <slot>
                        no popup content passed...
                    </slot>
                </div>
                <div class="modal-footer">
                    <slot name="popupmessage"></slot>
                    <button
                        type="button"
                        @click="closeBtnClicked"
                        class="btn btn-outline-secondary"
                        data-bs-dismiss="modal"
                        data-id="modalclosebutton">
                        {{ closetext }}
                    </button>
                    <slot name="okbutton"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    closetext: {
        type: String,
        default: ''
    },
    popupTitle: {
        type: String,
        default: ''
    },
    modalId: {
        type: String,
        default: ''
    },
    size: {
        type: String,
        default: 'default'
    }
});

const emit = defineEmits(['closeBtnClicked']);

const closeBtnClicked = () => {
    // event to parent, in two variants (backwards compatible)
    emit('closeBtnClicked');
};

const modalSize = computed(() => {
    const sizeMap = {
        'small': 'modal-sm',
        'default': '',
        'large': 'modal-lg',
        'extralarge': 'modal-xl',
        'xl': 'modal-xl'
    };

    return sizeMap[props.size.toLowerCase()] || '';
});
</script>

<style scoped>
</style>
