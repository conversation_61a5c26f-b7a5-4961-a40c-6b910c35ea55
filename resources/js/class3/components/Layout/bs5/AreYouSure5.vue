<template>
   <div
       class="modal fade"
       :id="modalId"
       tabindex="-1"
       aria-labelledby="modal-title"
       aria-hidden="true"
   >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-title">
                        {{ title === '' ? translate('generic.areyousure') : title }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    {{ message === '' ? translate('generic.thiscannotbeundone') : message }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" @click="cancelClicked">
                        {{ cancelText === '' ? ucFirst(translate('generic.cancel')) : cancelText }}
                    </button>
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" @click="confirmClicked">
                        {{ confirmText === '' ? ucFirst(translate('generic.ok')) : confirmText }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from "../../../composables/useLang.js";

const { translate, ucFirst } = useLang();

const emit = defineEmits(['confirmclicked', 'cancelclicked']);

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    message: {
        type: String,
        default: ''
    },
    modalId: {
        type: String,
        default: 'are-you-sure-modal'
    },
    confirmText: {
        type: String,
        default: ''
    },
    cancelText: {
        type: String,
        default: ''
    }
});

const confirmClicked = () => {
    emit('confirmclicked', true);
};

const cancelClicked = () => {
    emit('cancelclicked', false);
};

</script>

<style scoped>

</style>
