<template>
    <button
        @click.prevent="noop"
        class="btn btn-success btn-sm"
        data-bs-toggle="modal"
        :data-bs-target="'#' + modalId"
    >{{buttonLabel}}</button>
    <modal
        :size="size"
        :modal-id="modalId"
        :popup-title="popupTitle"
        :closetext="ucFirst(translate('generic.close'))"
    >
        <div class="mb-3">
            <div class="input-group searchbox">
                <span class="input-group-text" id="search-addon"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" :placeholder="translate('generic.searchfromlist')"
                       aria-label="Searchbox" aria-describedby="search-addon"
                       v-model="searchKey">
            </div>
        </div>
        <div v-if="busy">
            <spinner-svg />
        </div>
        <div v-else class="overflow-popup">
            <div v-for="(record, index) in filteredRecords" :key="index">
                <button class="btn btn-sm btn-link" @click.prevent="recordChosen(record.id)">
                    {{ showFieldsOfRecord(record) }}
                </button>
            </div>
        </div>
    </modal>
</template>

<script setup>
import Modal from '../Layout/bs5/Modal4.vue';
import SpinnerSvg from '../Layout/bs5/SpinnerSvgBs5.vue';
import { ref, computed, onMounted } from 'vue';
import useLang from "../../composables/useLang.js";
import useUtils from "../../composables/useUtils.js";
import useApi from "../../composables/useApi.js";
import useToast from "../../composables/useToast.js";

const { translate, ucFirst } = useLang();
const { uniqueId } = useUtils();
const { apiGet } = useApi();
const { failToast } = useToast();

// Props
const props = defineProps({
    size: {
        type: String,
        default: 'large'
    },
    excludeIds: {
        type: Array,
        default: () => []
    },
    popupTitle: {
        type: String,
        required: true
    },
    // comma separated list of fields
    displayFields: String,
    // api request to be called to retrieve te records
    apiGetString: {
        type: String,
        default: ''
    },
    useData: {
        type: Array,
        default: null
    },
    // label to show on toggle button
    buttonLabel: {
        type: String,
        default: 'Quick-Jump'
    }
});

// Emits
const emit = defineEmits(['record-chosen']);

// Reactive state
const records = ref([]);
const searchKey = ref('');
const busy = ref(false);

// Computed properties
const filteredRecords = computed(() => {
    return records.value.filter((record) => {
        return !props.excludeIds.includes(record.id) &&
            record.name.toLowerCase().includes(searchKey.value.toLowerCase());
    });
});

/**
 * In case this component is used more than once on a page
 * the modal's ID must be unique
 */
const modalId = computed(() => {
    return 'QJ_' + uniqueId();
});

// Methods
const getRecords = async () => {
    if (props.useData === null) {
        busy.value = true;
        try {
            const response = await apiGet(props.apiGetString);
            records.value = response.data.data;
        } catch (err) {
            failToast(`Error fetching records: ${err}`);
        } finally {
            busy.value = false;
        }
    } else {
        records.value = props.useData;
    }
};

/**
 * Merge fields to display into 1 string
 * @param record
 * @returns {string}
 */
const showFieldsOfRecord = (record) => {
    let retString = '';
    props.displayFields.split(',').forEach(fieldName => {
        let insertString = record[fieldName.trim()];
        // some tweaks for student groups
        insertString = insertString === null ? '' : insertString;
        if (insertString.charAt(0) === '-') insertString = insertString.substring(2);
        retString += insertString + ' - ';
    });
    return retString.slice(0, -3);
};

const recordChosen = (recordId) => {
    // Get the modal element
    const modalElement = document.getElementById(modalId.value);

    // Use Bootstrap 5 modal API to hide the modal
    const bsModal = bootstrap.Modal.getInstance(modalElement);
    if (bsModal) {
        bsModal.hide();
    }

    // Emit the event with the chosen record ID
    emit('record-chosen', recordId);
};

// Lifecycle hooks
onMounted(() => {
    getRecords();
});
</script>

<style scoped>
.overflow-popup {
    max-height: 500px;
    overflow-y: auto;
}
a {
    text-decoration: underline;
}
</style>
