<template>
<div>
    <div
        v-if="domainData?.schoolName"
        class="d-flex justify-content-between align-items-center"
    >
        <div class="text-right">
            <h3>{{ domainData.schoolName }}</h3>
            <div>{{ domainData.strAddress.address1 }}</div>
            <div>{{ domainData.strAddress.zip }} {{ domainData.strAddress.city }}</div>
            <div>{{ domainData.strAddress.telephone }}</div>
            <div>{{ domainData.strAddress.email }}</div>
        </div>
        <div>
            <img :src="domainData.logoUrl" alt="logo" class="img-fluid" />
        </div>
    </div>
    <hr>
</div>
</template>

<script setup>
import { onMounted } from "vue";
import useDomain from "../../composables/useDomain";

const { domainData, getDomainDataByAccessToken } = useDomain();

onMounted(() => {
    getDomainDataByAccessToken();
});
</script>

<style scoped>

</style>
