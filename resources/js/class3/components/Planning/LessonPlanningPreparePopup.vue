<template>
    <!-- todo: if the planned event is a trial lesson -->
    <!-- todo: if all lessons have been planned according to ends_after_nr_of_occurrences -->

    <modal
        :popup-title="ucFirst(translate('generic.prepareplanning'))"
        :size="'extralarge'"
        modal-id="LessonPlanningPreparePopup"
        :closetext="ucFirst(translate('generic.close'))"
    >
        <template #okbutton>
            <button
                class="btn btn-success"
                @click.prevent="persistEvents"
                :disabled="!analysisResult || eventsToPersist.length === 0"
                data-dismiss="modal"
            >
                <i class="fas fa-save"></i>
                {{ ucFirst(translate("generic.persistevents")) }} (
                {{ eventsToPersist.length }}
                {{ translateChoice("generic.events", eventsToPersist.length) }} )
            </button>
        </template>

        <template v-if="analysing">
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <span class="me-3">{{ translate("generic.analysing") }}</span>
                        <SpinnerSVG/>
                    </div>
                </div>
            </div>
        </template>

        <template v-else-if="!analysisResult">
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger">
                        {{ translate("generic.analysisfailed") }}
                    </div>
                </div>
            </div>
        </template>

        <template v-else>
            <panel>
                <template #title>
                    <strong>{{ ucFirst(translate("generic.analysis")) }}</strong>
                </template>
                <template #subtitle>
                    <button
                        class="btn btn-sm btn-primary"
                        @click="selectAllEvents"
                        :disabled="eventsToPersist.length === analysisResult.length"
                    >
                        {{ translate("generic.selectall") }}
                    </button>
                    <button
                        class="btn btn-sm btn-primary"
                        @click="selectNoEvents"
                        :disabled="eventsToPersist.length === 0"
                    >
                        {{ translate("generic.selectnone") }}
                    </button>
                </template>

                <div class="scrollable-section">
                    <table class="table table-condensed">
                        <thead>
                        <tr>
                            <th>{{ ucFirst(translate('generic.select')) }}</th>
                            <th>{{ ucFirst(translate("generic.datetime")) }}</th>
                            <th>{{ ucFirst(translateChoice("generic.locations", 1)) }}</th>
                            <th>{{ ucFirst(translateChoice("generic.tutors", 1)) }}</th>
                            <th>{{ ucFirst(translate("generic.remarks")) }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(event, eventDateTime) in analysisResult" :key="eventDateTime">
                            <td>
                                <input
                                    type="checkbox"
                                    @click="toggleEventToBePersisted(eventDateTime)"
                                    :checked="eventsToPersist.includes(eventDateTime)"
                                />
                            </td>
                            <td>{{ displayDateTime(eventDateTime) }}</td>
                            <td>{{ event.event.location_name }}</td>
                            <td>{{ event.event.tutor_name }}</td>
                            <td>
                                <div
                                    v-for="(remark, index) in event.remarks"
                                    :key="index"
                                    class="alert"
                                    :class="remark.severity === 'blocking' ? 'alert-danger' : 'alert-success'"
                                    style="padding: 0.2rem 0.5rem; margin-bottom: 0.2rem;"
                                >
                                    {{ remark.description }}
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </panel>
        </template>
    </modal>
</template>

<script setup>
import Modal from '../Layout/bs5/Modal4.vue';
import useLang from '../../composables/useLang';
import usePlanning from '../../composables/usePlanning';
import usePlanningCreateForm from '../../composables/usePlanningCreateForm';
import useDateTime from '../../composables/useDateTime';
import { watch } from 'vue';
import SpinnerSVG from '../Layout/bs5/SpinnerSvgBs5.vue';
import Panel from '../Layout/bs5/Panel4.vue';

const { translate, translateChoice, ucFirst } = useLang();
const {
    analyseData,
    analysing,
    analysisResult,
    eventsToPersist,
    persistEvents
} = usePlanning();

const {
    date,
    time,
    locationId,
    tutorId,
    locationIdAlt,
    repeats
} = usePlanningCreateForm();

const { displayDateTime } = useDateTime();
const props = defineProps({
    startAnalysis: {
        type: Boolean,
        default: false
    }
});

const toggleEventToBePersisted = (eventId) => {
    console.log('toggle event to be persisted');
    const index = eventsToPersist.value.indexOf(eventId);
    if (index === -1) {
        eventsToPersist.value.push(eventId);
    } else {
        eventsToPersist.value.splice(index, 1);
    }
};

const selectAllEvents = () => {
    eventsToPersist.value = [];
    Object.entries(analysisResult.value).forEach(([eventDateTime, event]) => {
        eventsToPersist.value.push(eventDateTime);
    });
};

const selectNoEvents = () => {
    eventsToPersist.value = [];
};

/**
 * Analyse the data and set the analysisResult
 */
watch(
    () => [props.startAnalysis],
    async (value, oldValue, onCleanup) => {
        if (value) {
            try {
                await analyseData(
                    date.value,
                    time.value,
                    locationId.value,
                    tutorId.value,
                    locationIdAlt.value,
                    repeats.value
                );
            } catch (e) {
                console.log('error in LessonPlanningPreparePopup.vue');
                console.error(e);
            }
        }
    }
);
</script>

<style scoped lang="scss">
@use '../../../../sass/tmpl3/variables' as *;

.skipped-dates {
    cursor: pointer;
    background-color: $classbgblue;
    color: white;
}
.scrollable-section {
    max-height: 600px;
    overflow-y: auto;
}
</style>
