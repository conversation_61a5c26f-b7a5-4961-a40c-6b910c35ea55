<template>
    <panel>
        <template v-slot:title>
            {{ucFirst(translate("generic.availability"))}} {{translateChoice('generic.students', 2)}}
        </template>
        <div class="row">
            <div class="col-2">
                <a :href="'/timetables/exportprefs/' + sortOrder" class="btn btn-success">
                    <i class="fa fa-file-excel"></i>
                    <div>{{ ucFirst(translate('generic.exporttoexcel')) }}</div>
                </a>
            </div>
            <div class="col-10">
                <label class="checklist-title">{{ ucFirst(translate('generic.sortby')) }}</label>
                <div class="form-check">
                    <input id="radioSortLastName" class="form-check-input"
                           type="radio" v-model="sortOrder" value="lastName">
                    <label for="radioSortLastName" class="form-check-label">
                        {{ ucFirst(translate('generic.lastname')) }}
                    </label>
                </div>
                <div class="form-check">
                    <input id="radioSortFirstName" class="form-check-input"
                           type="radio" v-model="sortOrder" value="firstName">
                    <label for="radioSortFirstName" class="form-check-label">
                        {{ ucFirst(translate('generic.firstname')) }}
                    </label>
                </div>
                <div class="form-check">
                    <input id="radioSortCourseName" class="form-check-input"
                           type="radio" v-model="sortOrder" value="courseName">
                    <label for="radioSortCourseName" class="form-check-label">
                        {{ ucFirst(translate('generic.coursename')) }}
                    </label>
                </div>
                <div class="form-check">
                    <input id="radioSortLastModified" class="form-check-input"
                           type="radio" v-model="sortOrder" value="lastModified">
                    <label for="radioSortLastModified" class="form-check-label">
                        {{ ucFirst(translate('generic.lastupdate')) }}
                    </label>
                </div>
            </div>
        </div>
    </panel>
</template>

<script setup>
import { ref } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import useLang from "../../composables/useLang.js";

const { translate, translateChoice, ucFirst } = useLang();

const sortOrder = ref('lastName');
</script>

<style scoped>
.checklist-title {
    font-weight: bold;
}
</style>
