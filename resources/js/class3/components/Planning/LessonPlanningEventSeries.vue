<template>
    <panel :busy="busyPlanning" class="panel-primary">
        <template #title>
            <strong>
                {{ ucFirst(translate("generic.sequence")) }}
                {{ translate("generic.tutoringevents") }}
            </strong>
            <button
                v-if="eventsToShow.length > 0"
                data-bs-toggle="modal"
                data-bs-target="#confirm-delete-future-appointments"
                class="btn btn-sm btn-warning ms-2"
            >
                <i class="fas fa-trash-alt"></i>
                {{ translate('generic.deleteallfutureappointments') }}
            </button>
        </template>
        <template #subtitle>
            <!-- select all or only future events-->
            <div class="form-check form-switch">
                <input 
                    class="form-check-input" 
                    type="checkbox" 
                    role="switch" 
                    id="showAllSwitch"
                    v-model="showAll"
                >
                <label class="form-check-label" for="showAllSwitch">
                    {{ isShowAllLabel }}
                </label>
            </div>
        </template>
        <!-- PANEL BODY -->
        <div v-if="eventsToShow.length > 0" class="d-flex flex-wrap">
            <!-- note: in drag-and-drop weekEvents is the targetWeek -->
            <div
                v-for="(weekEvents, weekIndex) in eventsByWeek"
                :key="weekIndex"
                :class="[
                    'week-container drop-zone',
                    { 'current-week': isCurrentWeek(weekEvents.weekNumber, weekEvents.year) }
                ]"
                @drop="dropTutoringEvent($event, weekEvents, weekIndex)"
                @dragover.prevent
                @dragenter.prevent="onDragEnter"
                @dragleave.prevent="onDragLeave"
            >
                <div class="week-header">
                    <p class="p-small">
                        Week {{ weekEvents.weekNumber }}
                        <span> {{ weekEvents.year }}</span>
                        <span v-if="weekEvents.events[0]"> - {{ getMonthName(weekEvents.events[0]?.start) }}</span>
                        <!-- prevent two new events when the first isn't saved yet -->
                        <button
                            v-if="shouldShowAddButton(weekEvents.weekNumber, weekEvents.year)"
                            v-tooltip="ucFirst(translate('generic.addextraevent'))"
                            @click="createExtraEvent(weekEvents.weekNumber, weekEvents.year, events); newWeekEventsCreatedWeekNumbers.push(weekEvents.weekNumber)"
                            class="btn btn-sm btn-primary extra-btn-style"
                        >
                            <font-awesome-icon icon="plus" />
                        </button>
                        <div
                            v-if="newWeekEventsCreatedWeekNumbers.includes(weekEvents.weekNumber)"
                            class="alert alert-sm alert-warning"
                        >
                            {{ ucFirst(translate('generic.neweventcreatedopentosave')) }}
                        </div>
                    </p>
                    <hr>
                    <!-- if we have a date exception in this week, show it's name-->
                    <p class="p-small"
                       v-html="showDateExceptionsForWeek(weekEvents.weekNumber, weekEvents.events[0]?.start.substring(0,4))"></p>
                </div>
                <div class="events-container d-flex">
                    <div v-for="tutoringEvent in weekEvents.events" :key="tutoringEvent.id" class="te-wrapper">
                        <tutoring-event
                            :tutoring-event="tutoringEvent"
                            :ref="el => { childComponents[tutoringEvent.id] = el; }"
                        />
                    </div>
                    <div v-if="weekEvents.events.length === 0" class="alert alert-info empty-week">
                        {{ translate('generic.notutoringevents') }}
                    </div>
                </div>
            </div>
        </div>
        <div v-else>
            <!-- no events at all or only no future events? -->
            <span v-if="events.length === 0" class="alert alert-info">
                {{ ucFirst(translate("generic.notutoringeventstoshowyet")) }}...
            </span>
            <span v-else class="alert alert-info">
                {{ ucFirst(translate("generic.onlypastappointments")) }}
            </span>
        </div>

        <are-you-sure
            :button-text="ucFirst(translate('generic.deleteallfutureappointments'))"
            modal-id="confirm-delete-future-appointments"
            @confirmclicked="deleteFutureAppointments()"
        ></are-you-sure>

        <EditEventC3 v-if="eventToEdit?.id" @updateCalendarEvents="updateCalendar"/>
    </panel>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import usePlanning from '../../composables/usePlanning';
import useEditEvents from '../../composables/useEditEvent';
import useLang from '../../composables/useLang';
import useDateTime from "../../composables/useDateTime";
import Panel from '../Layout/bs5/Panel4.vue';
import TutoringEvent from './LessonPlanningTutoringEvent.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import EditEventC3 from './EditEventC3.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const {
    busyPlanning,
    currentDateExceptions,
    currentOrFutureSchoolYear,
    deleteFutureAppointments,
    events,
    getEventsOfRegistrationForSchoolYear,
    getRelevantDateExceptions
} = usePlanning();
const { ucFirst, translate } = useLang();
const { getMonthName, getWeekNumber, getDateOfWeekByDayName, getHighestWeekNumber } = useDateTime()
const { createExtraEvent, eventToEdit, setDoRevert } = useEditEvents();
const childComponents = ref({});

const showAll = ref(false);
const isShowAllLabel = computed(() => {
    return showAll.value
        ? ucFirst(translate('generic.toggletoshowonlyfutureevents'))
        : ucFirst(translate('generic.toggletoshowallevents'));
});

const newWeekEventsCreatedWeekNumbers = ref([]);

/**
 * Get the current week number and year for highlighting
 */
const currentWeek = computed(() => {
    const now = new Date();
    const currentWeekNumber = getWeekNumber(now.toISOString());
    const currentYear = now.getFullYear();
    return {
        weekNumber: currentWeekNumber,
        year: currentYear
    };
});

/**
 * Check if a given week is the current week
 */
const isCurrentWeek = (weekNumber, year) => {
    return weekNumber === currentWeek.value.weekNumber && year === currentWeek.value.year;
};

/**
 * Check if a given week is in the past (before current week)
 */
const isWeekInPast = (weekNumber, year) => {
    const currentWeekNum = currentWeek.value.weekNumber;
    const currentYear = currentWeek.value.year;

    // If different years, compare years
    if (year !== currentYear) {
        return year < currentYear;
    }

    // Same year, compare week numbers
    return weekNumber < currentWeekNum;
};

/**
 * Check if the + button should be shown for a given week
 * Only show if no pending new events AND (showAll is true OR week is not in the past)
 */
const shouldShowAddButton = (weekNumber, year) => {
    const noPendingEvents = newWeekEventsCreatedWeekNumbers.value.length === 0;
    const weekAllowed = showAll.value || !isWeekInPast(weekNumber, year);
    return noPendingEvents && weekAllowed;
};

const updateCalendar = () => {
    getEventsOfRegistrationForSchoolYear();
    getRelevantDateExceptions();
    newWeekEventsCreatedWeekNumbers.value = [];
};

/**
 * filter the events to show only future events
 * @param {Array} events
 * @return {Array} eventsToShow
 */
const eventsToShow = computed(() => {
    const now = new Date();
    return showAll.value
        ? events.value
        : events.value.filter((event) => {
            return new Date(event.start) >= now;
        });
});
/**
 * find out which element is upcoming, nearest in the future
 * to highlight it in the list
 */
watch(eventsToShow, (eventsToShow) => {
    const now = new Date();
    const upcomingEvent = eventsToShow.find((event) => {
        return new Date(event.start) >= now;
    });
    if (upcomingEvent) {
        upcomingEvent.upcoming = true;
    }
});

/**
 * show the date exceptions for the requested week
 * @param weekNumber
 * @param year
 * @returns {string|string}
 */
const showDateExceptionsForWeek = (weekNumber, year) => {
    // get monday date of the requested week
    const fromDate = getDateOfWeekByDayName(weekNumber, year, 'Monday');
    const toDate = getDateOfWeekByDayName(weekNumber, year, 'Sunday');
    const dateExceptionsToShow = currentDateExceptions.value.filter((dateException) => {
        const deStart = new Date(dateException.datetime_start);
        const deEnd = new Date(dateException.datetime_end);
        const from = new Date(fromDate);
        const to = new Date(toDate);
        return ((deStart > from && deStart < to) || (deStart < from && deEnd > from));
    });
    return dateExceptionsToShow.length > 0
        ? `${ dateExceptionsToShow.map((dateException) => `
            <span class="alert alert-sm alert-warning ms-1">
                <a href="/dateexceptions?deid=${ dateException.id }" title="jump to date exception">
                    ${ dateException.reason }
                </a>
            </span>`).join('') }`
        : '';
};

/**
 * Re-group the events by week, fill in the blanks and order events in the same week
 * @param {Array} eventsToShow
 * @return {Array} eventsByWeek
 */
const eventsByWeek = computed(() => {
    const lowerValuesYear = currentOrFutureSchoolYear.value?.start_year;
    const upperValuesYear = currentOrFutureSchoolYear.value?.end_year;
    const firstWeekInStartYear = getWeekNumber(currentOrFutureSchoolYear.value?.start_date);
    const lastWeekInStartYear = getHighestWeekNumber(lowerValuesYear); // 52 or 53
    const lastWeekInUpperYear = getWeekNumber(currentOrFutureSchoolYear.value?.end_date);
    let eventsArray = getEmtyStartArrayOfWeeks(
        lowerValuesYear,
        firstWeekInStartYear,
        lastWeekInStartYear,
        upperValuesYear,
        1,
        lastWeekInUpperYear
    );

    // sort eventsToShow by start date to make sure we get the week numbers
    // in the correct order (week 1 somewhere in the middle in a typical school year)
    eventsToShow.value.sort((a, b) => {
        return new Date(a.start) - new Date(b.start);
    });
    // remodel the array to be grouped by week number.
    eventsToShow.value.forEach((event) => {
        const weekNumber = getWeekNumber(event.start);
        const year = parseInt(event.start.substring(0, 4));
        const existingWeek = eventsArray.find((week) => week.weekNumber === weekNumber && week.year === year);
        if (!existingWeek) {
            console.error('week not found in array', weekNumber, year);
            return;
        }
        existingWeek.events.push(event);
        // if we have more than 1 event in this week, sort them by start date.
        // this is unlikely to happen often
        // todo: check if this also works if there are date exceptions in the week
        if (existingWeek.events.length > 1) {
            existingWeek.events.sort((a, b) => {
                return new Date(a.start) - new Date(b.start);
            });
        }
    });

    // now sort the weeks by weekNumber
    // first split at first appearance in or after january,
    // so find the first in the array that has a week number that is lower than the one before
    const splitIndex = eventsArray.findIndex((week, index) => {
        return index > 0 && week.weekNumber < eventsArray[index - 1].weekNumber;
    });
    if (splitIndex > -1) {
        const firstPart = eventsArray.slice(0, splitIndex);
        const secondPart = eventsArray.slice(splitIndex);
        // now check if we are missing week numbers at the end of the start year
        if (firstPart[firstPart.length - 1].weekNumber < lastWeekInStartYear) {
            const missingWeeks = [];
            for (let i = firstPart[firstPart.length - 1].weekNumber + 1; i <= lastWeekInStartYear; i++) {
                missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
            }
            firstPart.push(...missingWeeks);
        } else {
            firstPart[firstPart.length - 1].year = lowerValuesYear;
        }
        // now check if we are missing a week in the consecutive numbering of weeks of the first part
        firstPart.forEach((week, index) => {
            if (index > 0 && week.weekNumber - firstPart[index - 1].weekNumber > 1) {
                // we are missing a week, so add it
                const missingWeeks = [];
                for (let i = firstPart[index - 1].weekNumber + 1; i < week.weekNumber; i++) {
                    missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
                }
                firstPart.splice(index, 0, ...missingWeeks);
            } else {
                // add the year to the week
                week.year = lowerValuesYear;
            }
        });

        // If this part does not start with week 1, we are missing weeks at the beginning.
        // Those weeks are in the previous year (lowerValuesYear)
        if (secondPart[0].weekNumber > 1) {
            const missingWeeks = [];
            for (let i = 1; i < secondPart[0].weekNumber; i++) {
                missingWeeks.push({ weekNumber: i, year: lowerValuesYear, events: [] });
            }
            secondPart.unshift(...missingWeeks);
        } else {
            // add the year to the week
            secondPart[0].year = lowerValuesYear;
        }
        // now check if we are missing a week in the consecutive numbering of weeks of the second part
        secondPart.forEach((week, index) => {
            if (index > 0 && week.weekNumber - secondPart[index - 1].weekNumber > 1) {
                // we are missing a week, so add it
                const missingWeeks = [];
                for (let i = secondPart[index - 1].weekNumber + 1; i < week.weekNumber; i++) {
                    missingWeeks.push({ weekNumber: i, year: upperValuesYear, events: [] });
                }
                secondPart.splice(index, 0, ...missingWeeks);
            } else {
                // add the year to the week
                week.year = upperValuesYear;
            }
        });
        eventsArray = [...firstPart, ...secondPart];
    }
    return eventsArray;
});

/**
 * Get an array of all weeks in a given date range (usually the school year)
 * with empty events. This is the basis to be filled with
 * actual events and date exceptions later
 * @param year1
 * @param startweek1
 * @param endWeek1
 * @param year2
 * @param startWeek2
 * @param endWeek2
 * @returns {*[]}
 */
const getEmtyStartArrayOfWeeks = (year1, startweek1, endWeek1, year2, startWeek2, endWeek2) => {
    const weeks = [];
    for (let i = startweek1; i <= endWeek1; i++) {
        weeks.push({ weekNumber: i, year: year1, events: [] });
    }
    for (let i = startWeek2; i <= endWeek2; i++) {
        weeks.push({ weekNumber: i, year: year2, events: [] });
    }
    return weeks;
};

const onDragEnter = (event) => {
    event.currentTarget.classList.add('drag-over');
};

const onDragLeave = (event) => {
    event.currentTarget.classList.remove('drag-over');
};

const dropTutoringEvent = (event, targetWeek) => {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');

    const data = JSON.parse(event.dataTransfer.getData("tutoring-event"));

    // if we have a target week, but it is the same as the originating week, do nothing
    const targetWeekNumber = targetWeek.weekNumber;
    const originatingWeekNumber = getWeekNumber(data.startsAt);
    if (targetWeekNumber === originatingWeekNumber) {
        return;
    }
    // Get the correct date for the event in the target week. Is this language safe?
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    // determine the correct date for the event in the target week (so the same day-of-week and the same time)
    // as the time stays the same, we don't need to do anything there, just the date needs to be changed
    const originatingDay = new Date(data.startsAt).getDay();
    let targetDate = getDateOfWeekByDayName(targetWeekNumber, parseInt(targetWeek.year), weekdays[originatingDay]);
    // add the original time to the target date
    targetDate = `${ targetDate } ${ data.startsAt.substring(11) }`;

    // now open the edit event dialog and paste the new start date
    eventToEdit.value = { ...data, start: targetDate };
    // get the correct component from ref and open the modal using doEditEvent
    const component = childComponents.value[data.eventId];

    if (component && typeof component.doEditEvent === 'function') {
        component.doEditEvent(targetDate);
    } else {
        // Fallback: try to find any component that has doEditEvent method
        for (const [key, comp] of Object.entries(childComponents.value)) {
            if (comp && typeof comp.doEditEvent === 'function') {
                comp.doEditEvent(targetDate);
                return;
            }
        }
        console.error('Could not find component to edit');
    }
};

/**
 * Cancel was pressed: revert the data
 */
setDoRevert(updateCalendar);

</script>

<style scoped lang="scss">
@use '../../../../sass/tmpl3/variables' as *;
.week-container {
    margin: 1rem 0 0 1rem;
    border: 1px solid $gray-300;
    padding: 1rem;
    flex: 1 0 auto;
    transition: all 0.2s ease;
}

.week-container.current-week {
    border: 3px solid $classblue;
    background-color: $gray-200;
    box-shadow: 0 0 10px $classlightestblue;
}

.week-container.drop-zone {
    /* Ensure the drop zone is properly configured */
    min-height: 200px;
}

.week-container.drag-over {
    border: 2px dashed $classlightblue;
    background-color: $classlightestblue;
    transform: scale(1.02);
}

.events-container {
    /* Make the elements in the week-container display in a row */
    display: flex;
    flex-direction: row;
    align-items: center;
}

.te-wrapper {
    /* Add margin to each event for better visibility */
    margin-right: 10px;
}

.empty-week {
    height: 8rem;
    width: 15rem;
    position: relative;
    margin-bottom: unset;
    text-align: center;
    padding-top: 2rem;
}

.p-small {
    font-size: 0.8rem;
    margin-bottom: 0.1rem
}
.extra-btn-style {
    float: right;
    padding: 0.15rem 0.5rem;
    border-radius: 50%;
    position: relative;
    top: -0.2rem;
}
</style>
