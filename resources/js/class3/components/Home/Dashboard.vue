<template>
    <!-- Content Row 1 -->
    <div class="row row-cols-1 row-cols-lg-3 g-4">
        <div class="col d-flex">
            <personal-calendar
                :tutor-id="tutorId"
                :tutor-name="tutorName"
                class="flex-fill"
            />
        </div>
        <div class="col d-flex">
            <alerts class="flex-fill" />
        </div>
        <div class="col d-flex">
            <birthdays class="flex-fill" />
        </div>
    </div>
    <!-- Content Row 2 -->
    <div class="row row-cols-1 row-cols-lg-2 g-4 mt-2">
        <div class="col d-flex">
            <search-students class="flex-fill" />
        </div>
        <div class="col d-flex">
            <search-courses class="flex-fill" />
        </div>
    </div>
</template>

<script setup>
import PersonalCalendar from "./PersonalCalendar.vue";
import Alerts from "./Alerts.vue";
import Birthdays from "./Birthdays.vue";
import SearchStudents from "../Students/SearchStudents.vue";
import SearchCourses from "../Courses/SearchCourses.vue";

defineProps({
    tutorId: {
        type: Number,
        required: true
    },
    tutorName: {
        type: String,
        required: true
    }
});
</script>

<style scoped>
/* Ensure cards stretch to fill the flex container height */
:deep(.card) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Make card body grow to fill remaining space */
:deep(.card-body) {
    flex: 1;
}
</style>
