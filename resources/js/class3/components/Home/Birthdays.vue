<template>
    <div class="card shadow mb-4 min-with-cards-3" data-testid="class-birthdays">
        <!-- Card Header - Dropdown -->
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 fw-bold text-primary">
                <i class="fa fa-birthday-cake"></i> {{ucFirst(translate('generic.birthdays'))}}
            </h6>
        </div>
        <!-- Card Body -->
        <div class="card-body">
            <div class="chart-area">
                <div v-for="(student, index) in birthdays" :key="index">
                    <span v-if="(student.age +1) === parseInt(student.threshold)">
                        <i class="fab fa-black-tie text-danger" 
                           v-tooltip="translate('generic.studentturns', {age: student.threshold})"></i>
                    </span>
                    <!-- it's my 21d birthday -->
                    <span v-if="student.itsMyBirthday && ((student.age) === parseInt(student.threshold))">
                        <i class="fas fa-birthday-cake text-danger"></i>
                        <i class="fab fa-black-tie text-danger" 
                           v-tooltip="translate('generic.studentis', {age: student.threshold})"></i>
                        {{translate('generic.itsmybirthday', {name: student.name})}} ({{ student.age }})
                    </span>
                    <!-- it's my birthday -->
                    <span v-else-if="student.itsMyBirthday">
                        <i class="fa fa-birthday-cake text-danger"></i>
                        {{translate('generic.itsmybirthday', {name: student.name})}} ({{ student.age }})
                    </span>
                    <span v-else>
                        {{ student.name }} {{translate('generic.willbe')}} {{ student.age +1 }} {{translate('generic.on')}} {{ student.date_of_birth.substr(8, 2) + "-" + student.date_of_birth.substr(5, 2)}}
                    </span>
                </div>
                <div v-if="birthdays.length === 0">
                    {{ucFirst(translate('generic.noupcomingbirthdays'))}}
                </div>
            </div>
        </div> <!-- end card-body -->
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import useToast from '../../composables/useToast.js';
import useLang from "../../composables/useLang.js";
import useApi from "../../composables/useApi.js";

const { failToast } = useToast();
const { ucFirst, translate } = useLang();
const { apiGet } = useApi();

const birthdays = ref([]);

onMounted(() => {
    getBirthdays();
});

const getBirthdays = async () => {
    try {
        const resp = await apiGet('/api/birthdays');
        if (typeof resp.data.birthdays !== 'undefined') {
            birthdays.value = resp.data.birthdays;
        }
    } catch (err) {
        failToast(`Error fetching birthdays: ${err}`);
    }
};
</script>
