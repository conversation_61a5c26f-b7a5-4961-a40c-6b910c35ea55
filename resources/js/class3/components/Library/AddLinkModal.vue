<template>
    <Modal
        modal-id="addLinkModal"
        :popup-title="isEditing ? ucFirst(translate('generic.editlink')) : ucFirst(translate('generic.addnewlink'))"
        :closetext="ucFirst(translate('generic.cancel'))"
        @closeBtnClicked="closeModal"
    >
        <div class="mb-3">
            <label for="linkLabel" class="form-label">{{ ucFirst(translate('generic.label')) }} *</label>
            <input 
                type="text" 
                id="linkLabel"
                class="form-control" 
                v-model="linkToAdd.label"
                :placeholder="ucFirst(translate('generic.enterlabel'))"
                maxlength="50"
            />
        </div>

        <div class="mb-3">
            <label for="linkUrl" class="form-label">{{ ucFirst(translate('generic.url')) }} *</label>
            <input 
                type="url" 
                id="linkUrl"
                :class="['form-control', { 'is-invalid': linkToAdd.url && !isValidUrl(linkToAdd.url) }]"
                v-model="linkToAdd.url"
                :placeholder="ucFirst(translate('generic.enterurl'))"
                maxlength="255"
            />
            <div v-if="linkToAdd.url && !isValidUrl(linkToAdd.url)" class="invalid-feedback">
                {{ ucFirst(translate('generic.pleaseentervalidurl')) }}
            </div>
        </div>

        <div class="mb-3">
            <label for="linkDescription" class="form-label">{{ ucFirst(translate('generic.description')) }}</label>
            <textarea 
                id="linkDescription"
                class="form-control" 
                v-model="linkToAdd.description"
                :placeholder="ucFirst(translate('generic.enterdescription'))"
                rows="3"
            ></textarea>
        </div>

        <template #okbutton>
            <button 
                type="button" 
                class="btn btn-primary" 
                @click="saveLink"
                :disabled="isSaveDisabled"
                data-bs-dismiss="modal"
            >
                {{ isEditing ? ucFirst(translate('generic.update')) : ucFirst(translate('generic.save')) }}
            </button>
        </template>
    </Modal>
</template>

<script setup>
import { computed } from 'vue';
import Modal from '../Layout/bs5/Modal4.vue';
import useDocuments from '../../composables/useDocuments';
import useLang from '../../composables/useLang';
import useToast from '../../composables/useToast';
import useUtils from '../../composables/useUtils';

// No props, no emits!

const { linkToAdd, storeLinkDocument, isEditing, cancelEditingLink } = useDocuments();
const { translate, ucFirst } = useLang();
const { successToast, failToast } = useToast();
const { isValidUrl } = useUtils();

const isSaveDisabled = computed(() => {
    return !linkToAdd.value.label?.trim() || !linkToAdd.value.url?.trim() || !isValidUrl(linkToAdd.value.url);
});

const closeModal = () => {
    cancelEditingLink();
};

const saveLink = async () => {
    try {
        await storeLinkDocument();
        successToast(isEditing.value ? 'Link updated successfully' : 'Link added successfully');
        // Close modal programmatically
        const modal = bootstrap.Modal.getInstance(document.getElementById('addLinkModal'));
        modal?.hide();
    } catch (error) {
        failToast(error);
    }
};
</script> 
