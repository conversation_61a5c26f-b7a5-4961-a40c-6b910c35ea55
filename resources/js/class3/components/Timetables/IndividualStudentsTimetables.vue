<template>
    <div class="card">
        <div class="card-header">
            <h5>
                {{ ucFirst(translate('generic.overviewindividualstudents')) }}
                <small class="text-muted">
                    <i class="fa fa-exclamation-circle me-1"></i>
                    {{ translate('generic.notriallessons') }}
                </small>
            </h5>
        </div>
        <div class="card-body">
            <div v-if="individualTimetables.length === 0 && !busy" class="alert alert-info">
                {{ ucFirst(translate('generic.notimetablesfound')) }}
            </div>
            <div v-else>
                <div class="row fw-bold mb-2">
                    <div class="col-md-2">{{ ucFirst(translate('generic.name')) }}</div>
                    <div class="col-md-2">{{ ucFirst(translate('generic.course')) }}</div>
                    <div class="col-md-3">{{ ucFirst(translateChoice('generic.recurrences', 1)) }}</div>
                    <div class="col-md-5">{{ ucFirst(translate('generic.eventsAndDateExceptions')) }}</div>
                </div>
                <timetable-row
                    v-for="timetable in individualTimetables"
                    :key="timetable.id"
                    :timetable="timetable"
                    :is-group="false"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from "../../composables/useLang";
import useTimetableReport from "../../composables/useTimetableReport";
import TimetableRow from "./TimetableRow.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, individualTimetables } = useTimetableReport();
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
