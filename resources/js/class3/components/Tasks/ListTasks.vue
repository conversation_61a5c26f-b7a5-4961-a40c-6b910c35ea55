<template>
    <panel>
        <template v-slot:title v-if="showActive">
            {{ ucFirst(translate('generic.opentasks')) }}
        </template>
        <template v-slot:title v-else>
            {{ ucFirst(translate('generic.closedtasks')) }}
        </template>

            <table class="table table-responsive-md">
                <thead>
                <tr>
                    <th class="text-center">{{ ucFirst(translate('generic.delete')) }}</th>
                    <th class="text-center">{{ ucFirst(translate('generic.edit')) }}</th>
                    <th>{{ ucFirst(translate('generic.assignedto')) }}</th>
                    <th>{{ ucFirst(translate('generic.tasktype')) }}</th>
                    <th>{{ ucFirst(translate('generic.studentname')) }}</th>
                    <th>{{ ucFirst(translate('generic.coursename')) }}</th>
                    <th>{{ ucFirst(translate('generic.startdate')) }}</th>
                    <th>{{ ucFirst(translate('generic.duedate')) }}</th>
                    <th v-if="!showActive">{{ ucFirst(translate('generic.enddate')) }}</th>
                </tr>
                </thead>
                <tbody>
                    <tr v-for="task in filteredTasks" :key="task.id">
                        <td class="text-center">
                            <button
                                class="btn btn-danger btn-sm"
                                data-bs-toggle="modal"
                                data-bs-target="#confirm-delete-task"
                                @click="taskToDelete=task.id"
                            >
                                <font-awesome-icon icon="trash" />
                            </button>
                        </td>
                        <td class="text-center">
                            <button
                                class="btn btn-success btn-sm"
                                @click="idToEdit = task.id"
                            >
                                <font-awesome-icon icon="edit" />
                            </button>
                        </td>
                        <td>{{ task.assignedTo?.name }}</td>
                        <td>{{ ucFirst(translate('generic.' + task.tasktype.description)) }}</td>
                        <td><a :href="'/students/' + task.student.id + '/edit'">{{ task.student?.name }}</a></td>
                        <td>{{ task.course?.name }}</td>
                        <td>{{ displayDate(task.date_opened) }}</td>
                        <td>{{ displayDate(task.date_due) }}</td>
                        <td v-if="!showActive">{{ displayDate(task.date_closed) }}</td>
                    </tr>
                </tbody>
            </table>
        </panel>
        <are-you-sure
            :button-text    = "ucFirst(translate('generic.deletetask'))"
            modal-id        = "confirm-delete-task"
            @confirmclicked = "deleteTask"
        ></are-you-sure>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import AreYouSure from '../Layout/bs5/AreYouSure4.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import useLang from '../../composables/useLang';
import useEditTask from '../../composables/useEditTask';
import useDateTime from '../../composables/useDateTime';

const props = defineProps({
    showActive: { // list open (true) or closed tasks
        type: Boolean,
        required: true
    },
    initOpen: {
        type: Number,
        default: 0
    }
});

const { ucFirst, translate } = useLang();
const { deleteTask, getAllTasks, idToEdit, tasks, taskToDelete } = useEditTask();
const { displayDate } = useDateTime();

const filteredTasks = computed(() => {
    const tasksToShow = tasks.value.filter(
        row => {
            return props.showActive
                ? row.date_closed == null || row.date_closed === '' || (new Date(row.date_closed) > new Date())
                : row.date_closed != null && row.date_closed !== '' && (new Date(row.date_closed) <= new Date());
        }
    );
    if (props.showActive) {
        // sort by date_due, unless there is no due date, then sort by date_opened
        // first split the tasksToShow into two arrays: with and without a due date
        const withDueDate = tasksToShow.filter(row => row.date_due != null && row.date_due !== '');
        const withoutDueDate = tasksToShow.filter(row => row.date_due == null || row.date_due === '');
        // then sort the two arrays
        withDueDate.sort((a, b) => new Date(a.date_due) - new Date(b.date_due));
        withoutDueDate.sort((a, b) => new Date(a.date_opened) - new Date(b.date_opened));
        // then merge the two arrays and return the result
        return withDueDate.concat(withoutDueDate);
    } else {
        // order by date_closed
        return tasksToShow.sort((a, b) => new Date(b.date_closed) - new Date(a.date_closed));
    }
});

onMounted(() => {
    getAllTasks();
    if (props.initOpen > 0) {
        idToEdit.value = props.initOpen;
    }
});

</script>

<style scoped>

</style>
