<template>
    <div id="targetForScrollIntoView">
        <panel v-if="task.id" :busy="busy">
            <template v-slot:title>
                {{ ucFirst(translate('generic.taskdata')) }}
            </template>
            <template v-slot:subtitle>
                {{ ucFirst(translate('generic.' + task.tasktype.description)) }}
            </template>
            <div class="row">
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">{{ `${ucFirst(translate('generic.studentname'))}` }}</label>
                        <input type="text" class="form-control" v-model="task.student.name" disabled>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">{{ `${ucFirst(translate('generic.coursename'))}` }}</label>
                        <input type="text" class="form-control" v-model="task.course.name" disabled>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-4">
                    <div class="mb-3">
                        <label class="form-label">{{ ucFirst(translate('generic.startdate')) }}</label>
                        <input type="text" class="form-control" :value="displayDate(task.date_opened)" disabled>
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label class="form-label">{{ ucFirst(translate('generic.duedate')) }}</label>
                        <span v-if="isClosed">
                            <input type="text" class="form-control" :value="displayDate(task.date_due)" disabled>
                        </span>
                        <VueDatepicker
                            v-else
                            v-model="task.date_due"
                            v-bind="dpOptionsDate"
                            :placeholder="translate('generic.clicktoedit')"
                            @open="dirty=true"
                        />
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-3">
                        <label class="form-label">{{ ucFirst(translate('generic.closingdate')) }}</label>
                        <input type="text" class="form-control" :value="endDate" disabled>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">{{ ucFirst(translate('generic.remarks')) }}</label>
                        <span v-if="isClosed" v-html="task.remarks"></span>
                        <textarea
                            v-else
                            class="form-control"
                            v-model="task.remarks"
                            @keydown="dirty=true"
                        ></textarea>
                    </div>
                </div>
            </div>
            <!-- assign to a user -->
            <div class="row">
                <div class="col-6">
                    <div class="mb-3">
                        <label class="form-label">{{ ucFirst(translate('generic.assignedto')) }}</label>
                        <span v-if="isClosed">
                            <span v-if="task.assigned_user_id != null">
                            {{ allActiveUsers.find(user => user.id === task.assigned_user_id).name }}
                            </span>
                            <span v-else>
                                {{ ucFirst(translate('generic.noone')) }}
                            </span>
                        </span>
                        <select
                            v-else
                            class="form-select"
                            v-model="task.assigned_user_id"
                            @change="dirty=true"
                        >
                            <option
                                v-for="user in allActiveUsers"
                                :key="user.id"
                                :value="user.id"
                            >
                                {{ user.name }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row" v-if="!isClosed">
                <div class="col-6">
                    <div class="mb-3">
                        <label class="form-label" v-html="ucFirst(translate('generic.finish'))"></label>
                        <div>
                            <button
                                class="btn btn-sm btn-danger"
                                @click="closeTask"
                            >
                                <font-awesome-icon icon="times-circle" class="me-1" />
                                {{ ucFirst(translate('generic.closethistask')) }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <button
                v-if="dirty && !busy && !isClosed"
                class="btn btn-sm btn-primary"
                @click="saveTask"
            >
                <font-awesome-icon icon="save" class="me-1" />
                {{ ucFirst(translate('generic.save')) }}
            </button>
            <span v-if="dirty && !isClosed" class="text-danger">
                <span v-if="busy">
                    <SpinnerSvg></SpinnerSvg>
                </span>
                {{ ucFirst(translate('generic.pleasesavechanges'))}}
            </span>
        </panel>
        <panel v-else>
            <template v-slot:title>
                {{ ucFirst(translate('generic.taskdata')) }}
            </template>
            {{ ucFirst(translate('generic.pickarecord'))}}...
        </panel>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import Panel from '../Layout/bs5/Panel4.vue';
import SpinnerSvg from '../Layout/bs5/SpinnerSvgBs5.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import moment from 'moment';
import VueDatepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

import useEditTask from '../../composables/useEditTask';
import useLang from '../../composables/useLang';
import useBaseData from '../../composables/useBaseData';
import useDatePicker from '../../composables/useDatePicker';
import useDateTime from "../../composables/useDateTime";

const { dpOptions: dpOptionsDate } = useDatePicker(true);
const { busy, dirty, saveTask, task } = useEditTask();
const { ucFirst, translate } = useLang();
const { allActiveUsers, initBaseData } = useBaseData();
const { displayDate } = useDateTime();

const endDate = computed(() => {
    const input = task.value?.date_closed;
    if (input == null || input?.length < 10) return '-';
    if (translate('generic.language') === 'nl') {
        return moment(input).format('DD-MM-YYYY');
    } else {
        return moment(input).format('YYYY-MM-DD');
    }
});
const closeTask = () => {
    task.value.date_closed = new Date().toISOString().substring(0, 10);
    dirty.value = true;
};

const isClosed = computed(() => {
    const now = new Date();
    return task.value?.date_closed && task.value?.date_closed < now.toISOString().substring(0, 10);
});
onMounted(async () => {
    await initBaseData({
        activeUsers: true
    });
});
</script>

<style scoped>
label {
    font-weight: bold;
}
</style>
