<?php

namespace Database\Seeders\ModelSeeders;

use Illuminate\Database\Seeder;
use App\Models\Domain;

class DomainSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create first domain
        Domain::create([
            'id'                    => 9,
            'domain_name'           => 'Kattenparadijs',
            'lookup_url'            => 'www.kattenparadijs.nl',
            'language'              => 'nl',
            'logo_url'              => '/images/test/cat-head.jpg',
            'website_url'           => 'https://www.kattenparadijs.nl',
            'rates_conditions_url'  => 'https://www.kattenparadijs.nl/algvw.pdf',
            'privacy_url'           => 'https://www.kattenparadijs.nl/privacy.pdf',
            'name'                  => 'Het Kattenparadijs',
            'address1'              => 'Spotweg 12',
            'address2'              => '',
            'zip'                   => '1122BB',
            'city'                  => 'Felisburgt',
            'telephone'             => '**********',
            'email'                 => '<EMAIL>',
            'adult_threshold'       => 18,
            'contact_person_name'   => '<PERSON>',
            'course_tax_rate'       => 21.0,
            'default_password'      => 'Katte(Katte$',
            'schedule_threshold'    => 30,
            'status'                => 'pro',
            'created_at'            => date('Y-m-d'),
            'updated_at'            => date('Y-m-d')
        ]);
        
        // Create a second domain to test trial account functions
        Domain::create([
            'id'                    => 10,
            'domain_name'           => 'Hondenparadijs',
            'lookup_url'            => 'www.hondenparadijs.nl',
            'language'              => 'nl',
            'logo_url'              => '/images/test/dog-head.jpg',
            'website_url'           => 'https://www.hondenparadijs.nl',
            'rates_conditions_url'  => 'https://www.hondenparadijs.nl/algvw.pdf',
            'privacy_url'           => 'https://www.hondenparadijs.nl/privacy.pdf',
            'name'                  => 'Het Hondenparadijs',
            'address1'              => 'Spotweg 14',
            'address2'              => '',
            'zip'                   => '1122BB',
            'city'                  => 'Felisburgt',
            'telephone'             => '**********',
            'email'                 => '<EMAIL>',
            'adult_threshold'       => 18,
            'contact_person_name'   => 'Janneke van Kanines',
            'course_tax_rate'       => 21.0,
            'default_password'      => 'Hond(Hond$',
            'schedule_threshold'    => 30,
            'status'                => 'trial',
            'created_at'            => date('Y-m-d'),
            'updated_at'            => date('Y-m-d')
        ]);
    }
}
