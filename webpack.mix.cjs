// This is a CommonJS wrapper for the ES Module webpack.mix.js
// It's used by Laravel Mix which expects a CommonJS module

const mix = require('laravel-mix');
const webpack = require('webpack');
const path = require('path');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
mix.webpackConfig({
    resolve: {
        extensions: ['.ts', '.vue', '.js'],
    },
    output: {
        hashFunction: "sha256"
    },
    stats: {
        children: false,
    }
});

/* remove console messages in production builds */
if (mix.inProduction()) {
    mix.version();
    mix.options({
        terser: {
            terserOptions: {
                compress: {
                    drop_console: true
                }
            }
        }
    });
} else {
    mix.sourceMaps()
}

// Only compile the new Vue 3 application
mix.js('resources/js/class3/app.js', 'public/js/tmpl3')
    .vue({ 
        version: 3,
        options: {
            css: {
                loaderOptions: {
                    sass: {
                        implementation: require('sass'),
                        sassOptions: {
                            api: 'modern'
                        }
                    }
                }
            }
        }
    })
    .sass('resources/sass/app.scss', 'public/css/tmpl3', {
        sassOptions: {
            api: 'modern'
        }
    });
